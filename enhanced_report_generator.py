#!/usr/bin/env python3
"""
Enhanced report generator for comprehensive multi-platform candidate research findings
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class EnhancedReportGenerator:
    """Generate comprehensive candidate reports with multi-platform analysis"""
    
    def __init__(self):
        self.platform_priorities = {
            'linkedin': 1,
            'github': 1,
            'twitter': 2,
            'stackoverflow': 2,
            'medium': 2,
            'dev.to': 2,
            'youtube': 3,
            'kaggle': 2,
            'personal_website': 3,
            'publications_research': 2
        }
    
    def generate_comprehensive_report(self, research_result, candidate_profile, job_requirements) -> str:
        """Generate comprehensive candidate report with multi-platform findings"""
        
        try:
            report_sections = []
            
            # Header
            report_sections.append(self._generate_report_header(candidate_profile, job_requirements))
            
            # Digital Footprint Summary
            report_sections.append(self._generate_digital_footprint_summary(research_result))
            
            # Platform-Specific Analysis
            report_sections.append(self._generate_platform_analysis(research_result))
            
            # Content & Expertise Analysis
            report_sections.append(self._generate_content_expertise_analysis(research_result))
            
            # Professional Presence Assessment
            report_sections.append(self._generate_professional_presence_assessment(research_result))
            
            # Verification Status
            report_sections.append(self._generate_verification_status(research_result))
            
            # Strategic Recommendations
            report_sections.append(self._generate_strategic_recommendations(research_result, job_requirements))
            
            return "\n\n".join(report_sections)
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return f"Error generating comprehensive report: {e}"
    
    def _generate_report_header(self, candidate_profile, job_requirements) -> str:
        """Generate report header with metadata"""
        
        return f"""# COMPREHENSIVE CANDIDATE RESEARCH REPORT

**Candidate:** {candidate_profile.full_name}
**Position:** {job_requirements.position_title}
**Report Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Research Scope:** Multi-platform digital presence analysis

---"""
    
    def _generate_digital_footprint_summary(self, research_result) -> str:
        """Generate digital footprint overview"""
        
        platforms_found = research_result.platforms_searched or []
        total_profiles = research_result.total_results_found or 0
        additional_profiles = research_result.additional_profiles or []
        
        # Count platforms by type
        platform_counts = {
            'professional': 0,
            'technical': 0,
            'content_creation': 0,
            'personal': 0
        }
        
        professional_platforms = ['linkedin', 'github']
        technical_platforms = ['stackoverflow', 'kaggle', 'medium', 'dev.to']
        content_platforms = ['youtube', 'twitter']
        personal_platforms = ['personal_website', 'instagram']
        
        for platform in platforms_found:
            if platform in professional_platforms:
                platform_counts['professional'] += 1
            elif platform in technical_platforms:
                platform_counts['technical'] += 1
            elif platform in content_platforms:
                platform_counts['content_creation'] += 1
            elif platform in personal_platforms:
                platform_counts['personal'] += 1
        
        summary = f"""## DIGITAL FOOTPRINT SUMMARY

**Overall Presence:** {total_profiles} verified profiles across {len(platforms_found)} platforms

**Platform Distribution:**
- Professional Networks: {platform_counts['professional']} platforms
- Technical Communities: {platform_counts['technical']} platforms  
- Content Creation: {platform_counts['content_creation']} platforms
- Personal/Portfolio: {platform_counts['personal']} platforms

**Platforms Discovered:** {', '.join(platforms_found) if platforms_found else 'None'}

**Digital Presence Score:** {self._calculate_digital_presence_score(research_result)}/10"""
        
        return summary
    
    def _generate_platform_analysis(self, research_result) -> str:
        """Generate detailed platform-specific analysis"""
        
        analysis_sections = ["## PLATFORM-SPECIFIC ANALYSIS"]
        
        # LinkedIn Analysis
        if research_result.linkedin_profile:
            linkedin_analysis = self._analyze_linkedin_profile(research_result.linkedin_profile)
            analysis_sections.append(f"### LinkedIn Profile\n{linkedin_analysis}")
        
        # GitHub Analysis
        if research_result.github_profile:
            github_analysis = self._analyze_github_profile(research_result.github_profile)
            analysis_sections.append(f"### GitHub Profile\n{github_analysis}")
        
        # Additional Platforms Analysis
        additional_profiles = research_result.additional_profiles or []
        for profile in additional_profiles:
            platform = profile.get('platform', 'Unknown')
            if platform not in ['publications_research']:
                platform_analysis = self._analyze_additional_platform(profile)
                analysis_sections.append(f"### {platform.replace('_', ' ').title()}\n{platform_analysis}")
        
        return "\n\n".join(analysis_sections)
    
    def _generate_content_expertise_analysis(self, research_result) -> str:
        """Generate content and expertise analysis"""
        
        all_themes = []
        expertise_indicators = []
        
        # Collect themes from all platforms
        additional_profiles = research_result.additional_profiles or []
        for profile in additional_profiles:
            profile_data = profile.get('profile_data', {})
            themes = profile_data.get('content_themes', [])
            all_themes.extend(themes)
            
            # Collect expertise indicators
            platform = profile.get('platform', '')
            if platform in ['stackoverflow', 'kaggle', 'medium', 'dev.to']:
                expertise_indicators.append(platform)
        
        # Count theme frequency
        theme_counts = {}
        for theme in all_themes:
            theme_counts[theme] = theme_counts.get(theme, 0) + 1
        
        # Sort themes by frequency
        top_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        analysis = f"""## CONTENT & EXPERTISE ANALYSIS

**Primary Content Themes:**"""
        
        if top_themes:
            for theme, count in top_themes:
                analysis += f"\n- {theme} (mentioned across {count} platforms)"
        else:
            analysis += "\n- No specific themes identified"
        
        analysis += f"""

**Technical Expertise Indicators:**"""
        
        if expertise_indicators:
            analysis += f"\n- Active on {len(expertise_indicators)} technical platforms: {', '.join(expertise_indicators)}"
        else:
            analysis += "\n- Limited technical platform presence"
        
        analysis += f"""

**Content Creation Activity:**
{self._assess_content_creation_activity(research_result)}

**Knowledge Sharing Level:** {self._assess_knowledge_sharing_level(research_result)}"""
        
        return analysis
    
    def _generate_professional_presence_assessment(self, research_result) -> str:
        """Generate professional presence assessment"""
        
        presence_score = self._calculate_professional_presence_score(research_result)
        
        assessment = f"""## PROFESSIONAL PRESENCE ASSESSMENT

**Overall Professional Presence Score:** {presence_score}/10

**Assessment Breakdown:**
- **Platform Diversity:** {self._assess_platform_diversity(research_result)}/3
- **Content Quality:** {self._assess_content_quality(research_result)}/3  
- **Professional Engagement:** {self._assess_professional_engagement(research_result)}/2
- **Verification Confidence:** {self._assess_overall_verification(research_result)}/2

**Professional Branding Strength:** {self._assess_professional_branding(research_result)}

**Industry Thought Leadership:** {self._assess_thought_leadership(research_result)}

**Networking & Community Engagement:** {self._assess_community_engagement(research_result)}"""
        
        return assessment
    
    def _generate_verification_status(self, research_result) -> str:
        """Generate verification status for all platforms"""
        
        verification_details = ["## VERIFICATION STATUS"]
        
        # LinkedIn verification
        if research_result.linkedin_profile:
            linkedin_confidence = research_result.linkedin_profile.get('verification_confidence', 0.7)
            verification_details.append(f"**LinkedIn:** {linkedin_confidence:.1%} confidence - {self._get_confidence_level(linkedin_confidence)}")
        
        # GitHub verification
        if research_result.github_profile:
            github_confidence = research_result.github_profile.get('verification_confidence', 0.7)
            verification_details.append(f"**GitHub:** {github_confidence:.1%} confidence - {self._get_confidence_level(github_confidence)}")
        
        # Additional platforms verification
        additional_profiles = research_result.additional_profiles or []
        for profile in additional_profiles:
            platform = profile.get('platform', 'Unknown')
            confidence = profile.get('verification_confidence', 0.5)
            verification_details.append(f"**{platform.replace('_', ' ').title()}:** {confidence:.1%} confidence - {self._get_confidence_level(confidence)}")
        
        overall_confidence = self._calculate_overall_verification_confidence(research_result)
        verification_details.append(f"\n**Overall Verification Confidence:** {overall_confidence:.1%} - {self._get_confidence_level(overall_confidence)}")
        
        return "\n".join(verification_details)
    
    def _generate_strategic_recommendations(self, research_result, job_requirements) -> str:
        """Generate strategic hiring recommendations"""
        
        recommendations = f"""## STRATEGIC HIRING RECOMMENDATIONS

**Priority Level:** {self._determine_priority_level(research_result, job_requirements)}

**Key Strengths:**
{self._identify_key_strengths(research_result)}

**Areas for Further Investigation:**
{self._identify_investigation_areas(research_result)}

**Cultural Fit Indicators:**
{self._assess_cultural_fit_indicators(research_result)}

**Risk Assessment:**
{self._assess_hiring_risks(research_result)}

**Recommended Next Steps:**
{self._recommend_next_steps(research_result, job_requirements)}"""
        
        return recommendations
    
    # Helper methods for calculations and assessments
    def _calculate_digital_presence_score(self, research_result) -> int:
        """Calculate overall digital presence score (1-10)"""
        base_score = min(len(research_result.platforms_searched or []) * 1.5, 6)
        quality_bonus = min(research_result.total_results_found * 0.5, 3)
        verification_bonus = self._calculate_overall_verification_confidence(research_result) * 1
        return min(int(base_score + quality_bonus + verification_bonus), 10)
    
    def _calculate_professional_presence_score(self, research_result) -> int:
        """Calculate professional presence score (1-10)"""
        diversity_score = self._assess_platform_diversity(research_result)
        content_score = self._assess_content_quality(research_result)
        engagement_score = self._assess_professional_engagement(research_result)
        verification_score = self._assess_overall_verification(research_result)
        return min(diversity_score + content_score + engagement_score + verification_score, 10)
    
    def _assess_platform_diversity(self, research_result) -> int:
        """Assess platform diversity (0-3)"""
        platforms = research_result.platforms_searched or []
        return min(len(platforms), 3)
    
    def _assess_content_quality(self, research_result) -> int:
        """Assess content quality (0-3)"""
        # Simplified assessment based on platform types
        technical_platforms = ['github', 'stackoverflow', 'medium', 'dev.to']
        platforms = research_result.platforms_searched or []
        technical_count = sum(1 for p in platforms if p in technical_platforms)
        return min(technical_count, 3)
    
    def _assess_professional_engagement(self, research_result) -> int:
        """Assess professional engagement (0-2)"""
        professional_platforms = ['linkedin', 'twitter', 'medium']
        platforms = research_result.platforms_searched or []
        engagement_count = sum(1 for p in platforms if p in professional_platforms)
        return min(engagement_count, 2)
    
    def _assess_overall_verification(self, research_result) -> int:
        """Assess overall verification (0-2)"""
        confidence = self._calculate_overall_verification_confidence(research_result)
        if confidence >= 0.8:
            return 2
        elif confidence >= 0.6:
            return 1
        else:
            return 0
    
    def _calculate_overall_verification_confidence(self, research_result) -> float:
        """Calculate overall verification confidence"""
        confidences = []
        
        if research_result.linkedin_profile:
            confidences.append(research_result.linkedin_profile.get('verification_confidence', 0.7))
        
        if research_result.github_profile:
            confidences.append(research_result.github_profile.get('verification_confidence', 0.7))
        
        additional_profiles = research_result.additional_profiles or []
        for profile in additional_profiles:
            confidences.append(profile.get('verification_confidence', 0.5))
        
        return sum(confidences) / len(confidences) if confidences else 0.5
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Get confidence level description"""
        if confidence >= 0.8:
            return "High Confidence"
        elif confidence >= 0.6:
            return "Medium Confidence"
        elif confidence >= 0.4:
            return "Low Confidence"
        else:
            return "Very Low Confidence"
    
    # Simplified assessment methods (would be expanded in full implementation)
    def _analyze_linkedin_profile(self, profile) -> str:
        return f"**URL:** {profile.get('url', 'N/A')}\n**Professional Summary:** {profile.get('headline', 'Not available')}"
    
    def _analyze_github_profile(self, profile) -> str:
        return f"**URL:** {profile.get('url', 'N/A')}\n**Repositories:** {profile.get('public_repos', 0)}\n**Followers:** {profile.get('followers', 0)}"
    
    def _analyze_additional_platform(self, profile) -> str:
        profile_data = profile.get('profile_data', {})
        return f"**URL:** {profile_data.get('url', 'N/A')}\n**Analysis:** {profile.get('analysis_summary', 'Profile found and analyzed')}"
    
    def _assess_content_creation_activity(self, research_result) -> str:
        content_platforms = ['youtube', 'medium', 'dev.to', 'twitter']
        platforms = research_result.platforms_searched or []
        content_count = sum(1 for p in platforms if p in content_platforms)
        
        if content_count >= 3:
            return "High - Active content creator across multiple platforms"
        elif content_count >= 2:
            return "Moderate - Some content creation activity"
        elif content_count >= 1:
            return "Limited - Minimal content creation"
        else:
            return "None - No content creation platforms identified"
    
    def _assess_knowledge_sharing_level(self, research_result) -> str:
        sharing_platforms = ['stackoverflow', 'medium', 'dev.to', 'github']
        platforms = research_result.platforms_searched or []
        sharing_count = sum(1 for p in platforms if p in sharing_platforms)
        
        if sharing_count >= 3:
            return "High"
        elif sharing_count >= 2:
            return "Moderate"
        elif sharing_count >= 1:
            return "Limited"
        else:
            return "Minimal"
    
    def _assess_professional_branding(self, research_result) -> str:
        return "Moderate - Consistent professional presence across platforms"
    
    def _assess_thought_leadership(self, research_result) -> str:
        return "Developing - Shows potential for industry thought leadership"
    
    def _assess_community_engagement(self, research_result) -> str:
        return "Active - Engaged in professional communities"
    
    def _determine_priority_level(self, research_result, job_requirements) -> str:
        score = self._calculate_professional_presence_score(research_result)
        if score >= 8:
            return "HIGH PRIORITY"
        elif score >= 6:
            return "MEDIUM PRIORITY"
        else:
            return "LOW PRIORITY"
    
    def _identify_key_strengths(self, research_result) -> str:
        return "- Strong digital presence across multiple platforms\n- Active in professional communities\n- Demonstrates technical expertise"
    
    def _identify_investigation_areas(self, research_result) -> str:
        return "- Verify technical skills through code review\n- Assess cultural fit through behavioral interviews\n- Check references from professional network"
    
    def _assess_cultural_fit_indicators(self, research_result) -> str:
        return "- Professional communication style\n- Active in learning communities\n- Shares knowledge with others"
    
    def _assess_hiring_risks(self, research_result) -> str:
        return "- Low risk based on comprehensive digital presence\n- Verification confidence levels are acceptable"
    
    def _recommend_next_steps(self, research_result, job_requirements) -> str:
        return "1. Schedule technical interview\n2. Conduct behavioral assessment\n3. Check professional references\n4. Review portfolio/code samples"
