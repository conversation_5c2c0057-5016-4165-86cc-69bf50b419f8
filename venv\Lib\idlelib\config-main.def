# ID<PERSON> reads several config files to determine user preferences.  This
# file is the default config file for general idle settings.
#
# When IDLE starts, it will look in
# the following two sets of files, in order:
#
#     default configuration files in idlelib
#     --------------------------------------
#     config-main.def         default general config file
#     config-extensions.def   default extension config file
#     config-highlight.def    default highlighting config file
#     config-keys.def         default keybinding config file
#
#     user configuration files in ~/.idlerc
#     -------------------------------------
#     config-main.cfg         user general config file
#     config-extensions.cfg   user extension config file
#     config-highlight.cfg    user highlighting config file
#     config-keys.cfg         user keybinding config file
#
# On Windows, the default location of the home directory ('~' above)
# depends on the version.  For Windows 10, it is C:\Users\<USER>