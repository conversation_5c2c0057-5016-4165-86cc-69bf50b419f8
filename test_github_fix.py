#!/usr/bin/env python3
"""
Test script to verify GitHub API fixes and research agent improvements
"""

import asyncio
import aiohttp
import logging
import os
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestGitHubAPI:
    """Test class to verify GitHub API functionality"""
    
    def __init__(self, github_token: str = None):
        self.github_token = github_token
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_github_api_auth(self) -> Dict[str, Any]:
        """Test GitHub API authentication and basic functionality"""
        
        if not self.session:
            raise RuntimeError("Session not initialized")
        
        # Prepare headers
        headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'AI-HR-Agent-Test/1.0'
        }
        
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'
            logger.info("Testing with GitHub token authentication")
        else:
            logger.warning("Testing without GitHub token (rate limited)")
        
        # Test API rate limit endpoint
        try:
            async with self.session.get(
                'https://api.github.com/rate_limit',
                headers=headers,
                timeout=10
            ) as response:
                if response.status == 200:
                    rate_limit_data = await response.json()
                    logger.info(f"GitHub API rate limit check successful: {response.status}")
                    logger.info(f"Core rate limit: {rate_limit_data['resources']['core']}")
                    return {
                        'auth_status': 'success',
                        'rate_limit': rate_limit_data['resources']['core'],
                        'authenticated': self.github_token is not None
                    }
                else:
                    logger.error(f"GitHub API rate limit check failed: {response.status}")
                    return {
                        'auth_status': 'failed',
                        'status_code': response.status,
                        'authenticated': self.github_token is not None
                    }
        
        except Exception as e:
            logger.error(f"GitHub API test error: {e}")
            return {
                'auth_status': 'error',
                'error': str(e),
                'authenticated': self.github_token is not None
            }
    
    async def test_user_lookup(self, username: str = 'octocat') -> Dict[str, Any]:
        """Test GitHub user lookup functionality"""
        
        if not self.session:
            raise RuntimeError("Session not initialized")
        
        headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'AI-HR-Agent-Test/1.0'
        }
        
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'
        
        try:
            api_url = f"https://api.github.com/users/{username}"
            
            async with self.session.get(api_url, headers=headers, timeout=15) as response:
                if response.status == 200:
                    user_data = await response.json()
                    logger.info(f"Successfully retrieved user data for: {username}")
                    return {
                        'lookup_status': 'success',
                        'username': user_data.get('login'),
                        'name': user_data.get('name'),
                        'public_repos': user_data.get('public_repos', 0),
                        'followers': user_data.get('followers', 0),
                        'created_at': user_data.get('created_at')
                    }
                elif response.status == 404:
                    logger.warning(f"GitHub user not found: {username}")
                    return {'lookup_status': 'not_found', 'username': username}
                elif response.status == 403:
                    logger.warning(f"GitHub API rate limit or access forbidden: {username}")
                    return {'lookup_status': 'rate_limited', 'username': username}
                else:
                    logger.error(f"GitHub API error {response.status} for user: {username}")
                    return {'lookup_status': 'error', 'status_code': response.status, 'username': username}
        
        except asyncio.TimeoutError:
            logger.error(f"GitHub API timeout for user: {username}")
            return {'lookup_status': 'timeout', 'username': username}
        except Exception as e:
            logger.error(f"GitHub user lookup error: {e}")
            return {'lookup_status': 'exception', 'error': str(e), 'username': username}

async def main():
    """Main test function"""
    
    # Get GitHub token from environment
    github_token = os.getenv('GITHUB_TOKEN')
    
    if not github_token:
        logger.warning("No GITHUB_TOKEN environment variable found - testing without authentication")
    else:
        logger.info("GitHub token found - testing with authentication")
    
    async with TestGitHubAPI(github_token) as test_api:
        # Test 1: Authentication and rate limits
        logger.info("=== Testing GitHub API Authentication ===")
        auth_result = await test_api.test_github_api_auth()
        print(f"Authentication test result: {auth_result}")
        
        # Test 2: User lookup
        logger.info("=== Testing GitHub User Lookup ===")
        user_result = await test_api.test_user_lookup('octocat')
        print(f"User lookup test result: {user_result}")
        
        # Test 3: Test with a real username if available
        if github_token:
            logger.info("=== Testing with authenticated user lookup ===")
            auth_user_result = await test_api.test_user_lookup('torvalds')
            print(f"Authenticated user lookup result: {auth_user_result}")
    
    logger.info("GitHub API tests completed")

if __name__ == "__main__":
    asyncio.run(main())
