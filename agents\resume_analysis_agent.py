# resume_analysis_agent_optimized.py
import asyncio
import logging
import json
import re
import os
import gc
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
import multiprocessing
import threading
import time
from functools import lru_cache

import PyPDF2
import pdfplumber
from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, log_agent_message, update_processing_stats,
    MemoryMonitor, BatchProcessor, LRUCache, DataCompressor
)

logger = logging.getLogger(__name__)

class OptimizedResumeAnalysisAgent:
    """
    High-performance resume analysis agent with:
    - Parallel file processing
    - Intelligent caching
    - Memory optimization
    - Batch AI processing
    - Streaming text extraction
    """
    
    def __init__(self, openai_api_key: str, cache=None, batch_processor=None):
        self.client = OpenAI(api_key=openai_api_key)
        self.model = "gpt-4"
        self.cache = cache or LRUCache(maxsize=200)
        self.batch_processor = batch_processor or BatchProcessor(batch_size=5, max_workers=4)
        
        # Performance settings
        self.max_file_workers = min(8, multiprocessing.cpu_count())
        self.max_ai_workers = 3  # Conservative for API rate limits
        self.chunk_size = 8192  # For streaming file reading
        self.max_text_length = 15000  # Increased for better extraction
        
        # Caching for expensive operations
        self.extraction_cache = LRUCache(maxsize=100)
        self.ai_cache = LRUCache(maxsize=50)
        
        # Text extraction optimizations
        self.text_extractors = {
            '.pdf': self._extract_pdf_optimized,
            '.docx': self._extract_docx_optimized,
            '.doc': self._extract_doc_optimized,
            '.txt': self._extract_txt_optimized
        }
        
        # Performance tracking
        self.extraction_times = {}
        self.ai_processing_times = {}
        
    async def analyze_resumes_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Main parallel resume analysis method"""
        
        try:
            logger.info("📄 Starting high-performance resume analysis")
            state = log_agent_message(state, "resume_analysis", "Starting parallel resume analysis", "info")
            
            start_time = time.time()
            
            # Phase 1: Parallel text extraction
            state = await self._extract_resume_texts_parallel(state)
            
            if not state.get("raw_resumes"):
                state["errors"].append("No text extracted from uploaded files")
                return state
            
            # Phase 2: Batch AI processing
            state = await self._parse_resumes_batch_ai(state)
            
            # Phase 3: Post-processing and optimization
            state = self._post_process_candidates_optimized(state)
            
            # Update performance statistics
            total_time = time.time() - start_time
            state = update_processing_stats(
                state,
                "resume_analysis",
                successfully_parsed=len(state.get("parsed_candidates", [])),
                parsing_failures=len(state.get("raw_resumes", [])) - len(state.get("parsed_candidates", [])),
                total_processing_time=total_time,
                parallel_processing=True,
                files_per_second=len(state.get("raw_resumes", [])) / total_time if total_time > 0 else 0,
                memory_optimized=True,
                cache_hits=self.extraction_cache.size() + self.ai_cache.size()
            )
            
            logger.info(f"✅ Parallel resume analysis completed in {total_time:.2f}s")
            logger.info(f"📊 Processed {len(state.get('parsed_candidates', []))} candidates")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Parallel resume analysis failed: {e}")
            state["errors"].append(f"Resume analysis failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    async def _extract_resume_texts_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Extract text from all files in parallel with optimization"""
        
        logger.info("🔍 Starting parallel text extraction")
        
        # Create extraction tasks
        extraction_tasks = []
        
        # Use asyncio event loop with thread pool for CPU-bound tasks
        loop = asyncio.get_event_loop()
        
        with ThreadPoolExecutor(max_workers=self.max_file_workers) as executor:
            for file_path in state["uploaded_files"]:
                task = loop.run_in_executor(
                    executor,
                    self._extract_single_file_cached,
                    file_path
                )
                extraction_tasks.append((file_path, task))
            
            # Process results as they complete
            raw_resumes = []
            completed_count = 0
            
            for file_path, task in extraction_tasks:
                try:
                    result = await task
                    
                    if result:
                        raw_resumes.append(result)
                        logger.info(f"✅ Extracted text from {os.path.basename(file_path)}")
                    else:
                        logger.warning(f"⚠️ No text extracted from {file_path}")
                        state["warnings"].append(f"No text extracted from {file_path}")
                    
                    completed_count += 1
                    
                    # Log progress for large batches
                    if len(state["uploaded_files"]) > 5 and completed_count % 2 == 0:
                        logger.info(f"📊 Extraction progress: {completed_count}/{len(state['uploaded_files'])}")
                    
                except Exception as e:
                    logger.error(f"❌ Extraction failed for {file_path}: {e}")
                    state["errors"].append(f"Failed to extract text from {file_path}: {str(e)}")
        
        state["raw_resumes"] = raw_resumes
        logger.info(f"📊 Successfully extracted text from {len(raw_resumes)} files")
        
        return state
    
    def _extract_single_file_cached(self, file_path: str) -> Optional[Dict[str, str]]:
        """Extract text from single file with caching"""
        
        try:
            # Create cache key based on file path and modification time
            file_stat = os.stat(file_path)
            cache_key = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
            
            # Check cache first
            cached_result = self.extraction_cache.get(cache_key)
            if cached_result:
                logger.debug(f"💾 Cache hit for {os.path.basename(file_path)}")
                return cached_result
            
            # Extract text
            start_time = time.time()
            filename = os.path.basename(file_path)
            file_extension = os.path.splitext(file_path)[1].lower()
            
            extractor = self.text_extractors.get(file_extension)
            if not extractor:
                logger.warning(f"⚠️ Unsupported file type: {file_extension}")
                return None
            
            text_content = extractor(file_path)
            
            extraction_time = time.time() - start_time
            self.extraction_times[filename] = extraction_time
            
            if text_content and text_content.strip():
                result = {
                    "filename": filename,
                    "content": text_content,
                    "file_path": file_path,
                    "extraction_time": extraction_time,
                    "content_length": len(text_content)
                }
                
                # Cache result
                self.extraction_cache.put(cache_key, result)
                
                logger.debug(f"✅ Extracted {len(text_content)} chars from {filename} in {extraction_time:.2f}s")
                return result
            else:
                logger.warning(f"⚠️ No text content extracted from {filename}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Extraction error for {file_path}: {e}")
            return None
    
    def _extract_pdf_optimized(self, file_path: str) -> str:
        """Optimized PDF text extraction with fallback methods"""
        
        text = ""
        
        # Method 1: pdfplumber with memory optimization
        try:
            with pdfplumber.open(file_path) as pdf:
                page_count = len(pdf.pages)
                
                # Process in chunks for memory efficiency
                chunk_size = min(5, page_count)
                
                for i in range(0, page_count, chunk_size):
                    chunk_text = ""
                    end_page = min(i + chunk_size, page_count)
                    
                    for j in range(i, end_page):
                        try:
                            page_text = pdf.pages[j].extract_text()
                            if page_text:
                                chunk_text += page_text + "\n"
                        except Exception as e:
                            logger.debug(f"Page {j} extraction error: {e}")
                            continue
                    
                    text += chunk_text
                    
                    # Clear chunk from memory
                    del chunk_text
                    
                    # Garbage collect every few chunks
                    if i % (chunk_size * 2) == 0:
                        gc.collect()
            
            if text.strip():
                return text
                
        except Exception as e:
            logger.debug(f"pdfplumber failed for {file_path}: {e}")
        
        # Method 2: PyPDF2 fallback
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        logger.debug(f"PyPDF2 page {page_num} error: {e}")
                        continue
                        
        except Exception as e:
            logger.debug(f"PyPDF2 failed for {file_path}: {e}")
        
        return text
    
    def _extract_docx_optimized(self, file_path: str) -> str:
        """Optimized DOCX text extraction"""
        
        try:
            from docx import Document
            
            doc = Document(file_path)
            
            # Extract text in batches for memory efficiency
            text_chunks = []
            batch_size = 50  # Process 50 paragraphs at a time
            
            for i in range(0, len(doc.paragraphs), batch_size):
                batch = doc.paragraphs[i:i+batch_size]
                batch_text = "\n".join([p.text for p in batch if p.text.strip()])
                
                if batch_text.strip():
                    text_chunks.append(batch_text)
                
                # Clear batch from memory
                del batch, batch_text
                
                # Periodic garbage collection
                if i % (batch_size * 2) == 0:
                    gc.collect()
            
            # Extract table content if present
            if doc.tables:
                table_texts = []
                for table in doc.tables[:5]:  # Limit to first 5 tables
                    table_text = ""
                    for row in table.rows:
                        row_text = " | ".join([cell.text.strip() for cell in row.cells])
                        if row_text.strip():
                            table_text += row_text + "\n"
                    if table_text.strip():
                        table_texts.append(table_text)
                
                if table_texts:
                    text_chunks.extend(table_texts)
            
            return "\n".join(text_chunks)
            
        except ImportError:
            logger.error("python-docx not installed. Cannot process DOCX files.")
            return ""
        except Exception as e:
            logger.error(f"DOCX extraction error: {e}")
            return ""
    
    def _extract_doc_optimized(self, file_path: str) -> str:
        """Optimized DOC file extraction (fallback to docx method)"""
        return self._extract_docx_optimized(file_path)
    
    def _extract_txt_optimized(self, file_path: str) -> str:
        """Optimized TXT file reading with streaming"""
        
        try:
            text = ""
            
            # Read file in chunks for memory efficiency
            with open(file_path, 'r', encoding='utf-8') as file:
                while True:
                    chunk = file.read(self.chunk_size)
                    if not chunk:
                        break
                    text += chunk
                    
                    # Prevent extremely large files from consuming too much memory
                    if len(text) > self.max_text_length * 2:
                        text = text[:self.max_text_length] + "... [truncated for memory efficiency]"
                        break
            
            return text
            
        except UnicodeDecodeError:
            # Fallback encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    return file.read(self.max_text_length)
            except Exception as e:
                logger.error(f"TXT fallback encoding failed: {e}")
                return ""
        except Exception as e:
            logger.error(f"TXT extraction error: {e}")
            return ""
    
    async def _parse_resumes_batch_ai(self, state: ThreadSafeState) -> ThreadSafeState:
        """Parse resumes using batch AI processing with optimization"""
        
        logger.info("🤖 Starting batch AI processing")
        
        raw_resumes = state["raw_resumes"]
        parsed_candidates = []
        
        # Create extraction prompt once for reuse
        extraction_prompt = self._create_extraction_prompt_optimized(state["job_requirements"])
        
        # Process in batches to manage API rate limits and memory
        batch_size = min(self.max_ai_workers, len(raw_resumes))
        
        for i in range(0, len(raw_resumes), batch_size):
            batch = raw_resumes[i:i + batch_size]
            
            logger.info(f"🔄 Processing AI batch {i//batch_size + 1}/{(len(raw_resumes) + batch_size - 1)//batch_size}")
            
            # Process batch concurrently
            batch_tasks = []
            
            for resume_data in batch:
                task = asyncio.create_task(
                    self._extract_with_openai_cached(
                        resume_data["content"],
                        extraction_prompt,
                        resume_data["filename"]
                    )
                )
                batch_tasks.append((resume_data, task))
            
            # Wait for batch completion
            for resume_data, task in batch_tasks:
                try:
                    extracted_info = await task
                    
                    if extracted_info:
                        candidate = self._create_candidate_profile_optimized(
                            extracted_info,
                            resume_data["filename"]
                        )
                        
                        if candidate:
                            parsed_candidates.append(candidate)
                            logger.info(f"✅ Successfully parsed: {candidate.full_name}")
                        else:
                            logger.warning(f"⚠️ Failed to create profile for: {resume_data['filename']}")
                            state["warnings"].append(f"Failed to create profile for: {resume_data['filename']}")
                    else:
                        logger.warning(f"⚠️ AI extraction failed for: {resume_data['filename']}")
                        state["warnings"].append(f"AI extraction failed for: {resume_data['filename']}")
                
                except Exception as e:
                    logger.error(f"❌ Error processing {resume_data['filename']}: {e}")
                    state["errors"].append(f"Error processing {resume_data['filename']}: {str(e)}")
            
            # Rate limiting between batches
            if i + batch_size < len(raw_resumes):
                await asyncio.sleep(1.0)  # 1 second between batches
            
            # Memory cleanup between batches
            gc.collect()
        
        state["parsed_candidates"] = parsed_candidates
        logger.info(f"🎯 Successfully parsed {len(parsed_candidates)} candidates")
        
        return state
    
    def _create_extraction_prompt_optimized(self, job_requirements) -> str:
        """Create optimized extraction prompt with job context"""
        
        # Cache the prompt if job requirements are the same
        job_hash = hashlib.md5(
            f"{job_requirements.position_title}{job_requirements.job_description}".encode()
        ).hexdigest()[:8]
        
        cache_key = f"prompt_{job_hash}"
        cached_prompt = self.ai_cache.get(cache_key)
        
        if cached_prompt:
            return cached_prompt
        
        # Key skills for focused extraction
        key_skills = job_requirements.required_skills[:10]  # Focus on top 10 skills
        
        prompt = f"""You are an expert resume parser optimized for speed and accuracy. Extract ALL information from this resume and return as JSON.

PRIORITY CONTEXT (Focus on these):
Position: {job_requirements.position_title}
Key Required Skills: {', '.join(key_skills)}
Company: {job_requirements.company_name}

EXTRACTION REQUIREMENTS - Extract ALL information including:

PERSONAL INFO:
- full_name, email, phone, location
- linkedin_url, github_url, portfolio_url

PROFESSIONAL:
- summary, current_position, current_company
- years_experience (calculate from work history)

SKILLS (Extract ALL mentioned):
- technical_skills (frameworks, tools, platforms)
- programming_languages
- frameworks, tools, databases, cloud_platforms
- soft_skills

EXPERIENCE (For each job):
- position, company, start_date, end_date, duration
- location, responsibilities[], technologies[], achievements[]

EDUCATION (For each degree):
- degree, major, university, graduation_year, gpa, location

PROJECTS:
- name, description, technologies[], duration, role, outcomes

LISTS (Use simple strings, NOT objects):
- certifications: ["Cert Name - Issuer - Date"]
- publications: ["Title - Journal - Date - Authors"]  
- achievements: ["Achievement description with context"]
- volunteer_work: ["Org - Role - Duration - Description"]

CRITICAL RULES:
- Return ONLY valid JSON
- Use empty strings "" for missing text
- Use empty arrays [] for missing lists  
- Use 0 for missing numbers
- Extract information EXACTLY as written
- Calculate years_experience from work history if not explicit
- For certifications/publications/achievements/volunteer_work: use descriptive strings

JSON STRUCTURE:
{{
    "full_name": "", "email": "", "phone": "", "location": "",
    "linkedin_url": "", "github_url": "", "portfolio_url": "",
    "summary": "", "current_position": "", "current_company": "",
    "years_experience": 0,
    "technical_skills": [], "programming_languages": [], "frameworks": [],
    "tools": [], "databases": [], "cloud_platforms": [], "soft_skills": [],
    "work_experience": [{{"position": "", "company": "", "start_date": "", "end_date": "", 
                         "duration": "", "location": "", "responsibilities": [], 
                         "technologies": [], "achievements": []}}],
    "education": [{{"degree": "", "major": "", "university": "", "graduation_year": "", 
                    "gpa": "", "location": ""}}],
    "projects": [{{"name": "", "description": "", "technologies": [], "duration": "", 
                   "role": "", "outcomes": ""}}],
    "certifications": [], "publications": [], "achievements": [], "volunteer_work": []
}}"""
        
        # Cache the prompt
        self.ai_cache.put(cache_key, prompt)
        
        return prompt
    
    async def _extract_with_openai_cached(self, resume_text: str, prompt: str, filename: str) -> Optional[Dict[str, Any]]:
        """Extract information using OpenAI with caching"""
        
        try:
            # Create cache key from content hash
            content_hash = hashlib.md5(resume_text.encode()).hexdigest()[:12]
            cache_key = f"ai_extract_{content_hash}"
            
            # Check cache first
            cached_result = self.ai_cache.get(cache_key)
            if cached_result:
                logger.debug(f"💾 AI cache hit for {filename}")
                return cached_result
            
            start_time = time.time()
            
            # Optimize text length for API efficiency
            if len(resume_text) > self.max_text_length:
                # Smart truncation - keep beginning and try to keep complete sections
                resume_text = self._smart_truncate_text(resume_text, self.max_text_length)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"Extract information from this resume:\n\n{resume_text}"}
                ],
                temperature=0.1,
                max_tokens=3500  # Increased for comprehensive extraction
            )
            
            processing_time = time.time() - start_time
            self.ai_processing_times[filename] = processing_time
            
            content = response.choices[0].message.content.strip()
            
            # Parse JSON with error handling
            try:
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    parsed_data = json.loads(json_str)
                else:
                    parsed_data = json.loads(content)
                
                # Clean and validate extracted data
                cleaned_data = self._clean_extracted_data_optimized(parsed_data)
                
                # Cache successful result
                self.ai_cache.put(cache_key, cleaned_data)
                
                logger.debug(f"✅ AI extraction completed for {filename} in {processing_time:.2f}s")
                return cleaned_data
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing error for {filename}: {e}")
                logger.debug(f"Response content: {content[:500]}...")
                return None
            
        except Exception as e:
            logger.error(f"OpenAI API error for {filename}: {e}")
            return None
    
    def _smart_truncate_text(self, text: str, max_length: int) -> str:
        """Smart text truncation preserving structure"""
        
        if len(text) <= max_length:
            return text
        
        # Try to find good break points
        break_points = [
            "\nEDUCATION", "\nSKILLS", "\nEXPERIENCE", 
            "\nPROJECTS", "\nCERTIFICATIONS", "\nACHIEVEMENTS"
        ]
        
        # Keep first part + try to include complete sections
        keep_length = max_length - 100  # Leave some buffer
        truncated = text[:keep_length]
        
        # Try to extend to next section boundary
        for break_point in break_points:
            next_section = text.find(break_point, keep_length)
            if next_section != -1 and next_section < max_length:
                truncated = text[:next_section]
                break
        
        return truncated + "\n... [truncated for processing efficiency]"
    
    def _clean_extracted_data_optimized(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimized data cleaning and validation"""
        
        # Ensure string list fields are properly formatted
        string_list_fields = ['certifications', 'publications', 'achievements', 'volunteer_work']
        
        for field in string_list_fields:
            if field in data and isinstance(data[field], list):
                cleaned_list = []
                for item in data[field]:
                    if isinstance(item, dict):
                        # Convert dict to string efficiently
                        item_str = self._dict_to_string_optimized(item, field)
                        cleaned_list.append(item_str)
                    elif isinstance(item, str) and item.strip():
                        cleaned_list.append(item.strip())
                
                data[field] = cleaned_list
        
        # Ensure numeric fields are valid
        if 'years_experience' in data:
            try:
                years = float(data['years_experience'])
                data['years_experience'] = max(0, min(50, years))  # Reasonable bounds
            except (ValueError, TypeError):
                data['years_experience'] = 0
        
        # Clean and deduplicate skill lists
        skill_fields = ['technical_skills', 'programming_languages', 'frameworks', 'tools', 'soft_skills']
        for field in skill_fields:
            if field in data and isinstance(data[field], list):
                # Remove duplicates and empty items, convert to lowercase for comparison
                seen = set()
                unique_skills = []
                for skill in data[field]:
                    skill_lower = str(skill).lower().strip()
                    if skill_lower and skill_lower not in seen:
                        seen.add(skill_lower)
                        unique_skills.append(str(skill).strip())
                
                data[field] = unique_skills[:20]  # Limit to top 20 per category
        
        return data
    
    def _dict_to_string_optimized(self, item_dict: Dict[str, Any], field_type: str) -> str:
        """Optimized dictionary to string conversion"""
        
        if field_type == 'certifications':
            parts = [
                item_dict.get('name', ''),
                item_dict.get('issuer', ''),
                item_dict.get('date', '')
            ]
            return ' - '.join(p for p in parts if p)
        
        elif field_type == 'publications':
            parts = [
                item_dict.get('title', ''),
                item_dict.get('journal', item_dict.get('conference', '')),
                item_dict.get('date', ''),
                item_dict.get('authors', '')
            ]
            return ' - '.join(p for p in parts if p)
        
        elif field_type == 'achievements':
            name = item_dict.get('name', item_dict.get('title', ''))
            desc = item_dict.get('description', '')
            return f"{name} - {desc}" if desc else name
        
        elif field_type == 'volunteer_work':
            parts = [
                item_dict.get('organization', ''),
                item_dict.get('role', ''),
                item_dict.get('duration', ''),
                item_dict.get('description', '')
            ]
            return ' - '.join(p for p in parts if p)
        
        else:
            # Fallback: join all non-empty values
            return ' - '.join(str(v) for v in item_dict.values() if v)
    
    def _create_candidate_profile_optimized(self, extracted_info: Dict[str, Any], filename: str) -> Optional[CandidateProfile]:
        """Create optimized candidate profile"""
        
        try:
            # Helper function for safe extraction
            def safe_get(key, default=""):
                value = extracted_info.get(key, default)
                return str(value).strip() if value else default
            
            def safe_get_list(key, default=None):
                if default is None:
                    default = []
                value = extracted_info.get(key, default)
                if isinstance(value, list):
                    return [str(item).strip() for item in value if str(item).strip()]
                return default
            
            def safe_get_int(key, default=0):
                try:
                    return int(extracted_info.get(key, default))
                except (ValueError, TypeError):
                    return default
            
            # Calculate years of experience if not provided
            years_exp = safe_get_int('years_experience', 0)
            if years_exp == 0:
                years_exp = self._calculate_experience_from_history(
                    extracted_info.get('work_experience', [])
                )
            
            # Create candidate profile with memory-efficient tuples
            candidate = CandidateProfile(
                # Basic Information
                full_name=safe_get('full_name'),
                email=safe_get('email'),
                phone=safe_get('phone'),
                location=safe_get('location'),
                linkedin_url=safe_get('linkedin_url'),
                github_url=safe_get('github_url'),
                portfolio_url=safe_get('portfolio_url'),
                
                # Professional Summary
                summary=safe_get('summary'),
                years_experience=years_exp,
                current_position=safe_get('current_position'),
                current_company=safe_get('current_company'),
                
                # Skills (converted to tuples for memory efficiency)
                technical_skills=tuple(safe_get_list('technical_skills')),
                programming_languages=tuple(safe_get_list('programming_languages')),
                frameworks=tuple(safe_get_list('frameworks')),
                tools=tuple(safe_get_list('tools')),
                soft_skills=tuple(safe_get_list('soft_skills')),
                
                # Experience and Education
                work_experience=tuple(extracted_info.get('work_experience', [])),
                education=tuple(extracted_info.get('education', [])),
                
                # Additional Information
                certifications=tuple(safe_get_list('certifications')),
                publications=tuple(safe_get_list('publications')),
                projects=tuple(extracted_info.get('projects', [])),
                achievements=tuple(safe_get_list('achievements')),
                volunteer_work=tuple(safe_get_list('volunteer_work')),
                
                # Extract companies for quick access
                companies=tuple(self._extract_companies_from_experience(
                    extracted_info.get('work_experience', [])
                )),
                
                # Metadata
                cv_filename=filename,
                extraction_timestamp=datetime.now().isoformat(),
                processing_status="parsed"
            )
            
            return candidate
            
        except Exception as e:
            logger.error(f"Error creating candidate profile for {filename}: {e}")
            return None
    
    def _calculate_experience_from_history(self, work_experience: List[Dict]) -> int:
        """Calculate years of experience from work history"""
        
        try:
            total_months = 0
            
            for exp in work_experience:
                duration = exp.get('duration', '')
                if duration:
                    # Parse duration strings like "2 years", "6 months", "2 years 3 months"
                    years_match = re.search(r'(\d+)\s*(?:year|yr)', duration, re.IGNORECASE)
                    months_match = re.search(r'(\d+)\s*(?:month|mo)', duration, re.IGNORECASE)
                    
                    if years_match:
                        total_months += int(years_match.group(1)) * 12
                    if months_match:
                        total_months += int(months_match.group(1))
            
            return max(0, round(total_months / 12))
            
        except Exception as e:
            logger.debug(f"Experience calculation error: {e}")
            return 0
    
    def _extract_companies_from_experience(self, work_experience: List[Dict]) -> List[str]:
        """Extract company names from work experience"""
        
        companies = []
        seen = set()
        
        for exp in work_experience:
            company = exp.get('company', '').strip()
            if company and company.lower() not in seen:
                companies.append(company)
                seen.add(company.lower())
        
        return companies
    
    def _post_process_candidates_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Optimized post-processing with validation and enhancement"""
        
        logger.info("🔧 Post-processing candidates")
        
        processed_candidates = []
        
        for candidate in state.get("parsed_candidates", []):
            try:
                # Validate and enhance candidate data
                if self._validate_candidate_minimal(candidate):
                    enhanced_candidate = self._enhance_candidate_data_optimized(candidate)
                    processed_candidates.append(enhanced_candidate)
                else:
                    logger.warning(f"⚠️ Candidate validation failed: {candidate.full_name}")
                    state["warnings"].append(f"Validation failed for: {candidate.full_name}")
                
            except Exception as e:
                logger.error(f"❌ Post-processing error for {candidate.full_name}: {e}")
                state["warnings"].append(f"Post-processing error for {candidate.full_name}: {str(e)}")
        
        state["parsed_candidates"] = processed_candidates
        
        # Memory cleanup
        gc.collect()
        
        return state
    
    def _validate_candidate_minimal(self, candidate: CandidateProfile) -> bool:
        """Minimal validation for candidate data"""
        
        # Must have name and at least some content
        if not candidate.full_name or len(candidate.full_name.strip()) < 2:
            return False
        
        # Must have some professional information
        has_experience = bool(candidate.work_experience or candidate.current_position)
        has_skills = bool(candidate.technical_skills or candidate.programming_languages)
        has_education = bool(candidate.education)
        
        # Must have at least one of: experience, skills, or education
        return has_experience or has_skills or has_education
    
    def _enhance_candidate_data_optimized(self, candidate: CandidateProfile) -> CandidateProfile:
        """Optimized candidate data enhancement"""
        
        # Extract additional URLs from summary if not already present
        if candidate.summary and not candidate.linkedin_url:
            linkedin_match = re.search(r'linkedin\.com/in/[\w-]+', candidate.summary, re.IGNORECASE)
            if linkedin_match:
                candidate.linkedin_url = f"https://{linkedin_match.group()}"
        
        if candidate.summary and not candidate.github_url:
            github_match = re.search(r'github\.com/[\w-]+', candidate.summary, re.IGNORECASE)
            if github_match:
                candidate.github_url = f"https://{github_match.group()}"
        
        return candidate
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the analysis"""
        
        return {
            "extraction_times": self.extraction_times,
            "ai_processing_times": self.ai_processing_times,
            "cache_stats": {
                "extraction_cache_size": self.extraction_cache.size(),
                "ai_cache_size": self.ai_cache.size()
            },
            "average_extraction_time": sum(self.extraction_times.values()) / len(self.extraction_times) if self.extraction_times else 0,
            "average_ai_time": sum(self.ai_processing_times.values()) / len(self.ai_processing_times) if self.ai_processing_times else 0
        }
    
    def cleanup(self):
        """Cleanup resources and caches"""
        
        self.extraction_cache.clear()
        self.ai_cache.clear()
        
        if self.batch_processor:
            self.batch_processor.cleanup()
        
        gc.collect()

# Export optimized components
__all__ = ['OptimizedResumeAnalysisAgent']