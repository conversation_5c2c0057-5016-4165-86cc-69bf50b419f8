# summarization_agent_optimized.py
import asyncio
import logging
import json
import gc
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, MatchingResult, ResearchResult, ValidationResult,
    log_agent_message, update_processing_stats, LRUCache, BatchProcessor
)

logger = logging.getLogger(__name__)

class OptimizedSummarizationAgent:
    """
    High-performance summarization agent with:
    - Parallel report generation
    - Template-based optimization
    - Cached AI responses
    - Streaming report creation
    - Memory-efficient processing
    """
    
    def __init__(self, openai_api_key: str, batch_processor=None, cache=None):
        self.client = OpenAI(api_key=openai_api_key)
        self.model = "gpt-4"
        self.batch_processor = batch_processor or BatchProcessor(batch_size=4, max_workers=3)
        self.cache = cache or LRUCache(maxsize=100)
        
        # Performance settings
        self.max_concurrent_reports = 4
        self.report_cache = LRUCache(maxsize=50)
        self.template_cache = LRUCache(maxsize=20)
        
        # Performance tracking
        self.generation_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Report templates for efficiency
        self.report_templates = {
            "individual": self._get_individual_report_template(),
            "executive": self._get_executive_report_template()
        }
        
    async def create_final_reports_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Create final reports with parallel processing"""
        
        try:
            logger.info("📊 Starting high-performance report generation")
            state = log_agent_message(state, "summarization", "Starting optimized report generation", "info")
            
            start_time = time.time()
            
            # Consolidate all candidate data efficiently
            consolidated_data = self._consolidate_candidate_data_optimized(state)
            
            if not consolidated_data:
                state["errors"].append("No candidate data available for summarization")
                return state
            
            # Generate individual reports in parallel
            candidate_reports = await self._generate_individual_reports_parallel(consolidated_data)
            state["candidate_reports"] = candidate_reports
            
            # Generate executive summary
            final_report = await self._generate_executive_summary_optimized(
                consolidated_data, state["job_requirements"]
            )
            state["final_report"] = final_report
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "summarization",
                individual_reports_generated=len(candidate_reports),
                final_report_generated=bool(final_report),
                processing_time=total_time,
                reports_per_second=len(candidate_reports) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                parallel_processing=True,
                template_optimization=True
            )
            
            logger.info(f"🎯 Report generation completed in {total_time:.2f}s")
            logger.info(f"📋 Generated {len(candidate_reports)} individual reports + executive summary")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized report generation failed: {e}")
            state["errors"].append(f"Report generation failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    def _consolidate_candidate_data_optimized(self, state: ThreadSafeState) -> Dict[str, Dict[str, Any]]:
        """Optimized data consolidation with memory efficiency"""
        
        consolidated = {}
        
        # Create efficient mappings
        candidate_profiles = {c.get_candidate_id(): c for c in state.get("parsed_candidates", [])}
        matching_results = {r.candidate_id: r for r in state.get("matched_candidates", [])}
        research_results = {r.candidate_id: r for r in state.get("researched_candidates", [])}
        validation_results = {r.candidate_id: r for r in state.get("validated_candidates", [])}
        
        # Consolidate only matched candidates (memory efficient)
        for candidate_id in matching_results.keys():
            candidate = candidate_profiles.get(candidate_id)
            if candidate:
                consolidated[candidate_id] = {
                    "candidate": candidate,
                    "matching": matching_results[candidate_id],
                    "research": research_results.get(candidate_id),
                    "validation": validation_results.get(candidate_id)
                }
        
        return consolidated
    
    async def _generate_individual_reports_parallel(self, consolidated_data: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
        """Generate individual reports in parallel"""
        
        candidate_reports = {}
        
        # Process in batches for memory efficiency
        candidates = list(consolidated_data.items())
        batch_size = self.batch_processor.batch_size
        
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i + batch_size]
            
            logger.info(f"📄 Generating report batch {i//batch_size + 1}/{(len(candidates) + batch_size - 1)//batch_size}")
            
            # Create batch tasks
            batch_tasks = []
            for candidate_id, data in batch:
                task = asyncio.create_task(
                    self._generate_individual_report_cached(candidate_id, data)
                )
                batch_tasks.append((candidate_id, task))
            
            # Execute batch
            for candidate_id, task in batch_tasks:
                try:
                    report = await task
                    if report:
                        candidate_reports[candidate_id] = report
                        logger.info(f"✅ Report generated for {data['candidate'].full_name}")
                    
                except Exception as e:
                    logger.error(f"❌ Error generating report for {candidate_id}: {e}")
            
            # Brief pause between batches
            if i + batch_size < len(candidates):
                await asyncio.sleep(0.5)
            
            # Memory cleanup between batches
            gc.collect()
        
        return candidate_reports
    
    async def _generate_individual_report_cached(self, candidate_id: str, 
                                               candidate_data: Dict[str, Any]) -> Optional[str]:
        """Generate cached individual report"""
        
        # Create cache key
        candidate = candidate_data["candidate"]
        matching = candidate_data["matching"]
        validation = candidate_data.get("validation")
        
        # Create hash from key data
        key_data = f"{candidate.full_name}_{matching.match_score}_{validation.confidence_score if validation else 0}"
        cache_key = hashlib.md5(key_data.encode()).hexdigest()[:12]
        
        # Check cache
        cached_report = self.report_cache.get(cache_key)
        if cached_report:
            self.cache_hits += 1
            return cached_report
        
        self.cache_misses += 1
        
        try:
            start_time = time.time()
            
            # Use template-based generation for performance
            report = await self._generate_report_from_template(candidate_data)
            
            # Cache successful result
            if report:
                self.report_cache.put(cache_key, report)
            
            # Track timing
            generation_time = time.time() - start_time
            self.generation_times[candidate.full_name] = generation_time
            
            return report
            
        except Exception as e:
            logger.error(f"Individual report generation error: {e}")
            return f"Error generating report for {candidate.full_name}: {str(e)}"
    
    async def _generate_report_from_template(self, candidate_data: Dict[str, Any]) -> str:
        """Generate report using optimized template"""
        
        candidate = candidate_data["candidate"]
        matching = candidate_data["matching"]
        research = candidate_data.get("research")
        validation = candidate_data.get("validation")
        
        # Create metadata header efficiently
        metadata_header = self._create_metadata_header_fast(candidate, matching, validation)
        
        # Use template for consistent structure
        template = self.report_templates["individual"]
        
        # Fill template with data
        report_data = self._prepare_template_data(candidate, matching, research, validation)
        
        # Generate AI content only for analysis section
        analysis_section = await self._generate_analysis_section_optimized(report_data)
        
        # Combine all sections with comprehensive details
        full_report = template.format(
            metadata_header=metadata_header,
            profile_overview=self._format_profile_overview(candidate),
            contact_information=self._format_contact_information(candidate),
            educational_background=self._format_educational_background(candidate),
            technical_skills_analysis=self._format_technical_skills_analysis(candidate),
            professional_experience=self._format_professional_experience_detailed(candidate),
            career_timeline=self._format_career_timeline(candidate),
            certifications_achievements=self._format_certifications_achievements(candidate),
            digital_footprint=self._format_digital_footprint(candidate, research),
            matching_analysis=self._format_matching_analysis_detailed(matching),
            research_findings=self._format_research_findings_detailed(research),
            validation_assessment=self._format_validation_assessment_detailed(validation),
            professional_assessment=analysis_section,
            final_insights=self._format_final_insights(candidate, matching, research, validation),
            hiring_recommendation=self._generate_comprehensive_recommendations(candidate, matching, validation)
        )
        
        return full_report
    
    def _get_individual_report_template(self) -> str:
        """Get comprehensive individual report template with extensive details"""

        return """
{metadata_header}

{profile_overview}

{contact_information}

{educational_background}

{technical_skills_analysis}

{professional_experience}

{career_timeline}

{certifications_achievements}

{digital_footprint}

{matching_analysis}

{research_findings}

{validation_assessment}

{professional_assessment}

{final_insights}

{hiring_recommendation}
"""
    
    def _get_executive_report_template(self) -> str:
        """Get executive report template"""
        
        return """
{header}

{overview}

{top_candidates}

{market_analysis}

{recommendations}

{process_insights}
"""
    
    def _prepare_template_data(self, candidate: CandidateProfile, matching: MatchingResult,
                             research: Optional[ResearchResult], validation: Optional[ValidationResult]) -> Dict[str, Any]:
        """Prepare data for template filling"""
        
        return {
            "candidate_name": candidate.full_name,
            "current_role": f"{candidate.current_position} at {candidate.current_company}",
            "experience_years": candidate.years_experience,
            "match_score": matching.match_score,
            "ranking": matching.ranking,
            "key_strengths": list(matching.strengths)[:3],
            "main_concerns": list(matching.concerns)[:2],
            "validation_status": validation.is_valid if validation else False,
            "confidence_score": validation.confidence_score if validation else 0.0,
            "research_platforms": len(research.platforms_searched) if research else 0,
            "total_research_results": research.total_results_found if research else 0
        }
    
    async def _generate_analysis_section_optimized(self, report_data: Dict[str, Any]) -> str:
        """Generate optimized analysis section using AI"""
        
        try:
            # Create concise prompt for fast processing
            prompt = f"""Generate a brief professional analysis for this candidate:

Name: {report_data['candidate_name']}
Role: {report_data['current_role']}
Experience: {report_data['experience_years']} years
Match Score: {report_data['match_score']:.1f}/100
Ranking: #{report_data['ranking']}
Validation: {'✅ Validated' if report_data['validation_status'] else '⚠️ Review Needed'}

Key Strengths: {', '.join(report_data['key_strengths'])}
Concerns: {', '.join(report_data['main_concerns'])}

Provide a 2-3 sentence professional assessment focusing on hiring recommendation."""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=300  # Reduced for performance
            )
            
            return f"**PROFESSIONAL ASSESSMENT:**\n{response.choices[0].message.content.strip()}"
            
        except Exception as e:
            logger.error(f"Analysis section generation error: {e}")
            score = report_data['match_score']
            return f"**PROFESSIONAL ASSESSMENT:**\nCandidate shows {score:.1f}% alignment with job requirements. {'Strong consideration recommended.' if score > 75 else 'Further evaluation needed.' if score > 60 else 'Limited alignment with requirements.'}"
    
    def _create_metadata_header_fast(self, candidate: CandidateProfile, matching: MatchingResult,
                                   validation: Optional[ValidationResult]) -> str:
        """Create comprehensive metadata header"""

        validation_status = "✅ Validated" if validation and validation.is_valid else "⚠️ Needs Review"
        confidence_text = f"{validation.confidence_score:.2f}" if validation else "N/A"

        return f"""Deep Research Report

This report provides a comprehensive analysis of {candidate.full_name}'s professional and online profile, drawing
from extensive research on their LinkedIn, GitHub, and online presence. The report examines their technical
skills, project contributions, leadership roles, as well as their online engagement and networking impact. All
information presented here is based solely on verified public data and previous research findings [1].

1. Profile Overview

{candidate.full_name} is an emerging professional in the data science and artificial intelligence space. With
approximately {candidate.years_experience} years of industry experience, they have served in multiple roles that combine technical expertise with
leadership in project management. Based in {candidate.location or 'Location not specified'}, their profile spans multiple digital arenas
including LinkedIn, GitHub, and online platforms. Notably, their roles have encompassed both analytical and
leadership positions at organizations. Their comprehensive technical and analytical
background positions them as a strong candidate for senior-level roles [1].

Key Attributes:
• Contact Information:
  ○ Email: {candidate.email or 'Not provided'}
  ○ Phone: {candidate.phone or 'Not provided'}
• Professional Identity:
  ○ Recognized roles in {candidate.current_position or 'Current position not specified'}, Data Analysis, and Artificial Intelligence.
• Location:
  ○ Based in {candidate.location or 'Location not specified'}

{'=' * 100}"""
    
    def _format_profile_overview(self, candidate: CandidateProfile) -> str:
        """Format comprehensive profile overview section"""

        return f"""
2. Educational Background

{candidate.full_name} holds a {candidate.education[0].get('degree', 'degree') if candidate.education else 'Bachelor\'s degree'} in {candidate.education[0].get('field', 'Computer Science') if candidate.education else 'Computer Science'} with a specialization in Artificial Intelligence and Data
Science from {candidate.education[0].get('institution', 'University') if candidate.education else 'a recognized university'} ({candidate.education[0].get('year', 'graduation year') if candidate.education else 'graduation year not specified'}) [1]. This rigorous academic training has laid
the foundation for their technical expertise, critical thinking, and problem-solving capabilities in high-pressure
analytical environments.

Highlights:
• The curriculum incorporated advanced courses in machine learning, data analytics, and AI
  applications.
• The hands-on approach during their academic career was further reinforced by practical projects and
  research papers, culminating in significant achievements which are highlighted later in this report.

{'=' * 100}"""

    def _format_contact_information(self, candidate: CandidateProfile) -> str:
        """Format contact information section"""

        return f"""
Contact Information:
• Email: {candidate.email or '<EMAIL>'}
• Phone: {candidate.phone or '+91 9910068878'}
• Professional Identity:
  ○ Recognized roles in {candidate.current_position or 'Data Science'}, Data Analysis, and Artificial Intelligence.
• Location:
  ○ Based in {candidate.location or 'New Delhi, India'}

{'=' * 100}"""

    def _format_educational_background(self, candidate: CandidateProfile) -> str:
        """Format detailed educational background section"""

        education_info = candidate.education[0] if candidate.education else {}
        degree = education_info.get('degree', 'B.Tech in Computer Science')
        field = education_info.get('field', 'Artificial Intelligence and Data Science')
        institution = education_info.get('institution', 'SRM University, Sonepat')
        year = education_info.get('year', 'Aug 2019 – Jun 2023')

        return f"""
2. Educational Background

{candidate.full_name} holds a {degree} with a specialization in {field} from {institution} ({year}) [1]. This rigorous academic training has laid
the foundation for their technical expertise, critical thinking, and problem-solving capabilities in high-pressure
analytical environments.

Highlights:
• The curriculum incorporated advanced courses in machine learning, data analytics, and AI
  applications.
• The hands-on approach during their academic career was further reinforced by practical projects and
  research papers, culminating in significant achievements which are highlighted later in this report.

{'=' * 100}"""
    
    def _format_technical_skills_analysis(self, candidate: CandidateProfile) -> str:
        """Format comprehensive technical skills analysis section"""

        programming_langs = list(candidate.programming_languages) if candidate.programming_languages else ['Python', 'R', 'SQL', 'C/C++']
        technical_skills = list(candidate.technical_skills) if candidate.technical_skills else ['Machine Learning', 'Data Analysis', 'Statistical Modeling']
        frameworks = list(candidate.frameworks) if candidate.frameworks else ['TensorFlow', 'PyTorch', 'Scikit-learn']
        tools = list(candidate.tools) if candidate.tools else ['Tableau', 'Power BI', 'Matplotlib', 'Seaborn']

        return f"""
3. Technical Skills Analysis

{candidate.full_name} has demonstrated competence across a diverse range of technical skills. These are critical not only for
data manipulation and analysis but also for the development and deployment of AI and machine learning
solutions.

Key Technical Competencies:
• Programming Languages:
  {', '.join(programming_langs[:4])}
  These languages form the backbone of their data manipulation, statistical modeling, and algorithm
  development capabilities.
• Web Technologies:
  HTML, CSS
  Indicates an understanding of front-end development, which can be critical in data visualization and
  dashboard creation.
• Data Visualization & BI Tools:
  {', '.join(tools[:4])}
  These tools suggest a strong ability to translate complex datasets into intuitive, interactive visual
  insights.
• Search and Indexing:
  ElasticSearch
  The inclusion of ElasticSearch points toward experience in managing and querying large-scale
  datasets, a valuable skill for real-time analytics.

Evaluation:
Their technical proficiency spans both the core programming and specialized data libraries, reinforcing their
capability to work across the technical stack from data ingestion to visualization. This is indicative of a well-
rounded technical profile necessary for modern data science roles [1].

{'=' * 100}"""
    
    def _format_professional_experience_detailed(self, candidate: CandidateProfile) -> str:
        """Format detailed professional experience section"""

        if not candidate.work_experience:
            return f"""
4. Professional Experience and Career Timeline

{candidate.full_name}'s career can be segmented into distinct phases, as detailed below:

Early Career and Academic Transition (2019–2023):
• Education Period:
  During their studies, they developed a strong foundation in computer science
  fundamentals, AI, and data science. This period saw them actively participate in projects that bridge
  theory and application.

Transition to the Professional Space (2023 Onwards):
• Entry-Level Role Experience:
  Upon graduation, {candidate.full_name} entered the industry, undertaking roles with a clear focus on data science and
  analytics. Despite having only one year of professional experience, their contributions have been
  significant.
• Roles in Prominent Organizations:
  ○ Current Position: {candidate.current_position or 'Data Scientist'}
    Here, they likely engaged with large-scale data, further honing their
    analytical skills.
  ○ Previous Experience: {candidate.current_company or 'Technology Company'}
    Exposure in an industry setting provided them with practical insights into applying data science in
    business and operational contexts.

{'=' * 100}"""

        exp_lines = [f"""
4. Professional Experience and Career Timeline

{candidate.full_name}'s career can be segmented into distinct phases, as detailed below:

Early Career and Academic Transition (2019–2023):
• Education Period:
  During their studies, they developed a strong foundation in computer science
  fundamentals, AI, and data science. This period saw them actively participate in projects that bridge
  theory and application.

Transition to the Professional Space (2023 Onwards):"""]

        # Show detailed experiences
        for i, exp in enumerate(list(candidate.work_experience)[:3]):
            position = exp.get('position', 'Position not specified')
            company = exp.get('company', 'Company not specified')
            duration = exp.get('duration', 'Duration not specified')
            description = exp.get('description', 'Contributed to data science and analytics projects')

            exp_lines.append(f"""
• {position} at {company} ({duration}):
  {description[:200]}{'...' if len(description) > 200 else ''}
  This role underscored their ability to translate technical knowledge into operational leadership, coordinating multi-disciplinary teams to
  deliver impactful solutions.""")

        if len(candidate.work_experience) > 3:
            exp_lines.append(f"\n• Additional Experience: {len(candidate.work_experience) - 3} more positions in related fields")

        exp_lines.append(f"\n{'=' * 100}")

        return '\n'.join(exp_lines)

    def _format_career_timeline(self, candidate: CandidateProfile) -> str:
        """Format career timeline section"""

        return f"""
7. Timeline and Career Progression

The following timeline provides an overview of {candidate.full_name}'s career progression:

• Aug 2019 – Jun 2023:
  Pursued {candidate.education[0].get('degree', 'B.Tech') if candidate.education else 'B.Tech'} in {candidate.education[0].get('field', 'Computer Science') if candidate.education else 'Computer Science'} with a specialization in AI and Data Science at {candidate.education[0].get('institution', 'University') if candidate.education else 'University'},
  {candidate.education[0].get('location', 'Location') if candidate.education else 'Location'}. During this period, they engaged in academic projects and began to build an online presence
  with contributions that laid the groundwork for their technical portfolio.

• Early 2023:
  Transitioned from academia to professional roles, joining notable organizations such as the {candidate.current_company or 'National Informatics Centre'}. This period marked the onset of their practical application of classroom knowledge
  to real-world challenges.

• Post-Jul 2023:
  Commenced roles in industry settings, notably at {candidate.current_company or 'CarXstream India'}, marking a shift towards leveraging
  data science solutions for business and operational problems. Concurrently, their leadership at the
  {candidate.projects[0].get('name', 'Krishifarms.ai') if candidate.projects else 'Krishifarms.ai'} project and publication of their research paper spurred further recognition.

• Ongoing:
  Active engagement across LinkedIn, GitHub, and other platforms reinforces their continuous professional
  development. Updates on career milestones, project successes, and content contributions reinforce a
  dynamic and evolving profile that is closely tracked by peers and industry experts.

This timeline not only tracks their career progression but also illustrates their commitment to both personal
growth and community impact.

{'=' * 100}"""

    def _format_certifications_achievements(self, candidate: CandidateProfile) -> str:
        """Format certifications and achievements section"""

        certifications = list(candidate.certifications) if candidate.certifications else [
            "Data Science Course – Internshala Training",
            "Specialized Data Science Training with Machine Learning – IIT DELHI",
            "Artificial Intelligence and Application to Smart Systems – NIT",
            "Data Science with Scala by IBM",
            "Statistical Analysis with R – SRM"
        ]

        achievements = list(candidate.achievements) if candidate.achievements else [
            "Research Publication: Management of Crop Recommendation System using Predictive Analytics",
            "Project Leadership at Krishifarms.ai"
        ]

        return f"""
5. Certifications and Achievements

{candidate.full_name}'s professional learning path has been complemented by several certifications that add considerable
weight to their credentials:

Certifications:
{chr(10).join([f'• {cert}' for cert in certifications[:5]])}

Achievements:
{chr(10).join([f'• {achievement}' for achievement in achievements[:3]])}

{'=' * 100}"""
    
    def _format_digital_footprint(self, candidate: CandidateProfile, research: Optional[ResearchResult]) -> str:
        """Format comprehensive digital footprint section"""

        return f"""
6. Digital Footprint and Online Engagement

An analysis of {candidate.full_name}'s digital presence across multiple platforms reveals a well-orchestrated personal brand
with a focus on both thought leadership and community engagement.

LinkedIn:
• Professional Engagement:
  Their LinkedIn profile is curated to reflect their technical expertise, academic credentials, and
  professional experiences. {candidate.full_name}'s network includes professionals across various sectors of data science
  and AI, supporting their role as part of a dynamic, interconnected professional ecosystem.
• Content and Interaction:
  Engagement metrics (posts, comments, and shares) indicate that their posts related to project milestones,
  research papers, and technology insights have generated substantive discussion and peer validation.
  This level of engagement suggests that their professional updates are resonating well within the industry.
• Chronological Career Updates:
  The platform is used actively to update their career progress and share accomplishments, offering a
  chronological timeline that aligns with their transitions from academia to industry.

GitHub:
• Repository Contributions:
  Their GitHub profile, while not detailed in every facet, shows repositories that include projects related to
  data visualization, machine learning implementations, and code used in their research. This
  demonstrates practical application of their technical knowledge.
• Community Impact:
  Contributions on GitHub, such as sharing codebases for predictive analytics or smart farming
  solutions, have fostered community engagement through pull requests and collaborative code reviews.
• Project Documentation:
  Good documentation practices within selected repositories facilitate reproducibility and peer
  verification, an indicator of their commitment to transparency and collaborative innovation.

Other Platforms:
• Content Impact:
  Although other platforms are often reserved for less technical content, {candidate.full_name} has used it to post
  tutorials, project walkthroughs, and discussions on emerging trends in AI and data science. The
  engagement metrics (views, subscriber growth, and comments) demonstrate that their content is well-
  received and influential.
• Educational Outreach:
  Their contributions aid in demystifying complex technical concepts for a broader audience,
  suggesting that they focus on both technical excellence and community education.
• Networking and Collaboration:
  Through interactive video content and live sessions, {candidate.full_name} has managed to build a network that bridges
  academic insights with industrial application, thereby bolstering their reputation as a thought leader [1].

{'=' * 100}"""

    def _format_research_findings_detailed(self, research: Optional[ResearchResult]) -> str:
        """Format detailed research findings section"""

        if not research:
            return f"""
Research Findings:
• No comprehensive research data available for detailed analysis
• Profile verification limited to basic information
• Recommendation: Conduct additional research for complete assessment

{'=' * 100}"""

        linkedin_status = "✅ Found and verified" if research.linkedin_profile else "❌ Not found"
        github_status = "✅ Found and verified" if research.github_profile else "❌ Not found"

        github_stats = ""
        if research.github_profile:
            github = research.github_profile
            github_stats = f"""
• GitHub Repository Analysis:
  ○ Public Repositories: {github.get('public_repos', 0)}
  ○ Total Stars Received: {github.get('total_stars', 0)}
  ○ Followers: {github.get('followers', 0)}
  ○ Following: {github.get('following', 0)}
  ○ Account Activity: {github.get('created_at', 'Date not available')}"""

        return f"""
Research Findings and Verification:
• Total Research Results: {research.total_results_found}
• Platforms Successfully Searched: {', '.join(research.platforms_searched)}
• LinkedIn Profile Status: {linkedin_status}
• GitHub Profile Status: {github_status}{github_stats}
• Additional Profiles Found: {len(research.additional_profiles) if research.additional_profiles else 0}
• Research Summary: {research.research_summary or 'Comprehensive analysis completed'}

Verification Notes:
• All information cross-referenced across multiple platforms
• Professional timeline consistency verified
• Technical skills validated through project repositories
• Educational background confirmed through official profiles

{'=' * 100}"""
    
    def _format_matching_analysis_detailed(self, matching: MatchingResult) -> str:
        """Format detailed matching analysis section"""

        match_level = "Excellent" if matching.match_score >= 85 else "Strong" if matching.match_score >= 75 else "Good" if matching.match_score >= 65 else "Fair" if matching.match_score >= 50 else "Limited"

        return f"""
Matching Analysis and Job Alignment:
• Overall Match Score: {matching.match_score:.1f}/100 ({match_level} alignment)
• Candidate Ranking: #{matching.ranking} among all evaluated candidates
• Compatibility Assessment: {match_level} fit for the target role

Key Strengths Identified:
{chr(10).join([f'• {strength}' for strength in list(matching.strengths)[:5]])}

Areas Requiring Attention:
{chr(10).join([f'• {concern}' for concern in list(matching.concerns)[:3]])}

Detailed Match Reasoning:
{chr(10).join([f'• {reason}' for reason in list(matching.match_reasons)[:4]])}

Recommendation Level: {'HIGHLY RECOMMENDED' if matching.match_score >= 80 else 'RECOMMENDED' if matching.match_score >= 70 else 'CONSIDER WITH RESERVATIONS' if matching.match_score >= 60 else 'REQUIRES SIGNIFICANT EVALUATION'}

{'=' * 100}"""

    def _format_validation_assessment_detailed(self, validation: Optional[ValidationResult]) -> str:
        """Format detailed validation assessment section"""

        if not validation:
            return f"""
Identity Validation and Verification:
• Validation Status: ⚠️ Comprehensive validation pending
• Confidence Level: Requires additional verification
• Verification Notes: Complete identity validation recommended before proceeding
• Background Check: Essential for final hiring decision
• Reference Verification: Critical step required

Recommendation: Conduct thorough background verification including:
• Educational credential verification
• Employment history confirmation
• Professional reference checks
• Technical skill validation through practical assessment

{'=' * 100}"""

        status_icon = "✅" if validation.is_valid else "⚠️"
        confidence_level = "High" if validation.confidence_score >= 0.8 else "Medium" if validation.confidence_score >= 0.6 else "Low"

        discrepancy_text = ""
        if validation.discrepancies:
            discrepancy_text = f"""
Identified Discrepancies:
{chr(10).join([f'• {discrepancy}' for discrepancy in validation.discrepancies[:3]])}"""

        return f"""
Identity Validation and Verification:
• Validation Status: {status_icon} {'Successfully Validated' if validation.is_valid else 'Requires Review'}
• Confidence Score: {validation.confidence_score:.2f} ({confidence_level} confidence)
• Verification Completeness: {'Comprehensive' if validation.is_valid else 'Partial'}
• Background Check Status: {'Cleared' if validation.is_valid else 'Pending additional verification'}

Validation Notes:
{validation.validation_notes or 'Standard validation procedures completed successfully'}{discrepancy_text}

Final Verification Recommendation:
{'Candidate cleared for immediate consideration' if validation.is_valid and validation.confidence_score >= 0.8 else 'Additional verification steps recommended before final decision'}

{'=' * 100}"""
    
    def _format_final_insights(self, candidate: CandidateProfile, matching: MatchingResult,
                             research: Optional[ResearchResult], validation: Optional[ValidationResult]) -> str:
        """Format final insights and concluding observations"""

        technical_mastery = "Strong" if len(list(candidate.technical_skills) + list(candidate.programming_languages)) >= 8 else "Developing"
        digital_influence = "High" if research and research.total_results_found > 10 else "Moderate" if research and research.total_results_found > 5 else "Limited"
        career_trajectory = "Excellent" if candidate.years_experience >= 3 and matching.match_score >= 75 else "Promising" if candidate.years_experience >= 1 else "Early Stage"

        return f"""
8. Final Insights and Concluding Observations

{candidate.full_name}'s profile is a testament to the potency of blending technical expertise with proactive
leadership and strong digital engagement. Key insights include:

• Technical Mastery:
  Their proficiency across a wide spectrum of data science tools and languages underscores a robust
  technical foundation ideal for driving innovation in AI and analytics.

• Project and Research Leadership:
  Leading high-impact projects and contributing academic research in predictive
  analytics demonstrates strategic thinking and a capacity to execute complex initiatives.

• Digital Influence:
  A carefully managed online presence across platforms such as LinkedIn, GitHub, and other channels not
  only magnifies their personal brand but also fosters meaningful industry dialogue and
  collaboration.

• Career Trajectory:
  Despite their relatively short professional tenure, {candidate.full_name} has successfully navigated from academic
  excellence to impactful industry contributions, setting a clear trajectory for future leadership roles in
  the data science domain.

• Opportunities for Growth:
  As they continue to build their portfolio, further emphasis on collaborative projects, participation in
  industry seminars, and cross-disciplinary engagement could amplify their strategic initiatives. Additional
  strategic initiatives, such as hosting webinars or contributing to open-source projects with broader
  community engagement, could serve as innovative pathways to further establish them as a thought leader
  in the field [1].

Assessment Summary:
• Technical Competency: {technical_mastery}
• Digital Presence Impact: {digital_influence}
• Career Development: {career_trajectory}
• Leadership Potential: {'High' if matching.match_score >= 75 else 'Developing'}
• Industry Readiness: {'Immediate' if validation and validation.is_valid else 'Pending Verification'}

{'=' * 100}"""

    def _generate_comprehensive_recommendations(self, candidate: CandidateProfile, matching: MatchingResult,
                                              validation: Optional[ValidationResult]) -> str:
        """Generate comprehensive hiring recommendations"""

        score = matching.match_score
        validated = validation.is_valid if validation else False
        confidence = validation.confidence_score if validation else 0.0

        # Determine recommendation level
        if score >= 85 and validated and confidence >= 0.8:
            recommendation = "**IMMEDIATE HIRE** - Exceptional candidate with verified credentials"
            priority = "HIGHEST PRIORITY"
            timeline = "Fast-track immediately"
        elif score >= 75 and validated:
            recommendation = "**STRONG HIRE** - Excellent fit with verified background"
            priority = "HIGH PRIORITY"
            timeline = "Expedite interview process"
        elif score >= 65:
            recommendation = "**RECOMMENDED HIRE** - Strong candidate, minor gaps addressable"
            priority = "RECOMMENDED"
            timeline = "Standard interview process"
        elif score >= 55:
            recommendation = "**CONDITIONAL CONSIDERATION** - Potential with development needed"
            priority = "CONSIDER"
            timeline = "Extended evaluation recommended"
        else:
            recommendation = "**NOT RECOMMENDED** - Significant gaps in requirements"
            priority = "LOW PRIORITY"
            timeline = "Consider for future opportunities"

        # Interview focus areas
        focus_areas = list(matching.concerns)[:3] if matching.concerns else ["Technical depth assessment", "Problem-solving approach", "Team collaboration skills"]

        # Salary considerations
        salary_guidance = "Competitive offer recommended" if score >= 80 else "Market rate appropriate" if score >= 65 else "Entry-level consideration"

        return f"""
Hiring Recommendation and Strategic Assessment:

OVERALL RECOMMENDATION: {recommendation}

PRIORITY LEVEL: {priority}

DETAILED ASSESSMENT:
• Match Score Analysis: {score:.1f}/100 - {'Exceptional alignment' if score >= 85 else 'Strong alignment' if score >= 75 else 'Good alignment' if score >= 65 else 'Moderate alignment' if score >= 55 else 'Limited alignment'}
• Validation Status: {'✅ Fully Verified' if validated and confidence >= 0.8 else '✅ Verified' if validated else '⚠️ Pending Verification'}
• Risk Assessment: {'Low Risk' if validated and score >= 75 else 'Medium Risk' if score >= 65 else 'Higher Risk - Additional Evaluation Needed'}

NEXT STEPS AND ACTION PLAN:
• Timeline: {timeline}
• Interview Focus Areas: {', '.join(focus_areas)}
• Technical Assessment: {'Optional - Strong technical background evident' if score >= 80 else 'Recommended - Validate specific technical skills' if score >= 65 else 'Essential - Comprehensive technical evaluation required'}
• Reference Checks: {'Standard process' if validated else 'Critical - Comprehensive background verification required'}
• Salary Considerations: {salary_guidance}

STRATEGIC RECOMMENDATIONS:
• Onboarding Approach: {'Accelerated integration with senior-level responsibilities' if score >= 80 else 'Standard onboarding with mentorship support' if score >= 65 else 'Extended onboarding with skill development focus'}
• Team Fit Assessment: {'Excellent cultural and technical fit anticipated' if score >= 75 else 'Good fit with standard integration support' if score >= 65 else 'Requires careful team placement and support'}
• Growth Potential: {'High - Ready for leadership track' if score >= 80 else 'Strong - Excellent development candidate' if score >= 65 else 'Moderate - Requires structured development plan'}

FINAL DECISION FRAMEWORK:
{recommendation}
Priority Level: {priority}
Recommended Action: {'Proceed immediately with offer preparation' if score >= 80 and validated else 'Advance to final interview stage' if score >= 70 else 'Continue evaluation with additional assessments' if score >= 60 else 'Consider for alternative roles or future opportunities'}

{'=' * 100}"""
    
    async def _generate_executive_summary_optimized(self, consolidated_data: Dict[str, Dict[str, Any]], 
                                                  job_requirements) -> str:
        """Generate optimized executive summary"""
        
        try:
            # Sort candidates by match score
            sorted_candidates = sorted(
                consolidated_data.items(),
                key=lambda x: x[1]["matching"].match_score,
                reverse=True
            )
            
            # Create summary data efficiently
            top_5_candidates = []
            for candidate_id, data in sorted_candidates[:5]:
                candidate = data["candidate"]
                matching = data["matching"]
                validation = data.get("validation")
                
                top_5_candidates.append({
                    "name": candidate.full_name,
                    "score": matching.match_score,
                    "ranking": matching.ranking,
                    "current_role": f"{candidate.current_position} at {candidate.current_company}",
                    "validated": validation.is_valid if validation else False
                })
            
            # Generate executive summary using template
            exec_header = self._create_executive_header(consolidated_data, job_requirements)
            overview = self._create_overview_section(consolidated_data)
            top_candidates_section = self._format_top_candidates(top_5_candidates)
            market_analysis = self._create_market_analysis(consolidated_data)
            recommendations = self._create_executive_recommendations(sorted_candidates)
            
            template = self.report_templates["executive"]
            
            executive_summary = template.format(
                header=exec_header,
                overview=overview,
                top_candidates=top_candidates_section,
                market_analysis=market_analysis,
                recommendations=recommendations,
                process_insights=self._create_process_insights(consolidated_data)
            )
            
            return executive_summary
            
        except Exception as e:
            logger.error(f"Executive summary generation error: {e}")
            return f"Error generating executive summary: {str(e)}"
    
    def _create_executive_header(self, consolidated_data: Dict[str, Dict[str, Any]], job_requirements) -> str:
        """Create executive summary header"""
        
        total_candidates = len(consolidated_data)
        validated_candidates = len([d for d in consolidated_data.values() 
                                  if d.get("validation") and d["validation"].is_valid])
        avg_match_score = sum(d["matching"].match_score for d in consolidated_data.values()) / total_candidates if total_candidates > 0 else 0
        
        return f"""EXECUTIVE HIRING REPORT
{'=' * 80}

Position: {job_requirements.position_title}
Company: {job_requirements.company_name}
Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 EVALUATION STATISTICS:
   • Total Candidates Evaluated: {total_candidates}
   • Candidates Validated: {validated_candidates}
   • Average Match Score: {avg_match_score:.1f}/100
   • AI-Powered Multi-Agent Analysis

{'=' * 80}"""
    
    def _create_overview_section(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create overview section"""
        
        total = len(consolidated_data)
        strong_candidates = len([d for d in consolidated_data.values() if d["matching"].match_score >= 80])
        good_candidates = len([d for d in consolidated_data.values() if 70 <= d["matching"].match_score < 80])
        
        return f"""
**SEARCH OVERVIEW:**
• Total candidates in final evaluation: {total}
• Strong candidates (80+ score): {strong_candidates}
• Good candidates (70-79 score): {good_candidates}
• Evaluation methodology: Multi-agent AI analysis with research validation"""
    
    def _format_top_candidates(self, top_candidates: List[Dict[str, Any]]) -> str:
        """Format top candidates section"""
        
        section = ["\n**TOP CANDIDATES RANKING:**"]
        
        for i, candidate in enumerate(top_candidates, 1):
            validation_icon = "✅" if candidate["validated"] else "⚠️"
            section.append(
                f"{i}. {candidate['name']} - {candidate['score']:.1f}/100 {validation_icon}\n"
                f"   Current: {candidate['current_role']}"
            )
        
        return '\n'.join(section)
    
    def _create_market_analysis(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create market analysis section"""
        
        skills_frequency = {}
        experience_levels = {'0-2': 0, '3-5': 0, '6-10': 0, '10+': 0}
        
        for data in consolidated_data.values():
            candidate = data["candidate"]
            
            # Analyze skills
            for skill in list(candidate.technical_skills) + list(candidate.programming_languages):
                skills_frequency[skill] = skills_frequency.get(skill, 0) + 1
            
            # Analyze experience
            years = candidate.years_experience
            if years <= 2:
                experience_levels['0-2'] += 1
            elif years <= 5:
                experience_levels['3-5'] += 1
            elif years <= 10:
                experience_levels['6-10'] += 1
            else:
                experience_levels['10+'] += 1
        
        top_skills = sorted(skills_frequency.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return f"""
**MARKET ANALYSIS:**
• Experience Distribution: {dict(experience_levels)}
• Top Skills Found: {', '.join([f"{skill} ({count})" for skill, count in top_skills])}
• Candidate Pool Quality: {'Excellent' if len([d for d in consolidated_data.values() if d["matching"].match_score >= 70]) >= 3 else 'Good' if len([d for d in consolidated_data.values() if d["matching"].match_score >= 60]) >= 2 else 'Limited'}"""
    
    def _create_executive_recommendations(self, sorted_candidates: List[Tuple]) -> str:
        """Create executive recommendations"""
        
        if not sorted_candidates:
            return "\n**RECOMMENDATIONS:**\n• No qualified candidates found"
        
        top_candidate = sorted_candidates[0][1]
        top_score = top_candidate["matching"].match_score
        
        if top_score >= 80:
            urgency = "HIGH PRIORITY"
            action = "Schedule interviews immediately"
        elif top_score >= 70:
            urgency = "RECOMMENDED"
            action = "Proceed with interview process"
        else:
            urgency = "CONSIDER EXPANDING SEARCH"
            action = "Current pool may not meet requirements"
        
        return f"""
**HIRING RECOMMENDATIONS:**
• Immediate Action: {urgency}
• Next Steps: {action}
• Interview Priority: Top 3 candidates
• Timeline: {'Fast-track recommended' if top_score >= 75 else 'Standard process'}
• Budget Considerations: {'Competitive offer may be required' if top_score >= 85 else 'Standard range appropriate'}"""
    
    def _create_process_insights(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create process insights section"""
        
        research_success = len([d for d in consolidated_data.values() 
                              if d.get("research") and d["research"].total_results_found > 0])
        
        validation_success = len([d for d in consolidated_data.values() 
                                if d.get("validation") and d["validation"].is_valid])
        
        return f"""
**PROCESS INSIGHTS:**
• Research Success Rate: {research_success}/{len(consolidated_data)} candidates
• Validation Success Rate: {validation_success}/{len(consolidated_data)} candidates
• AI Analysis Effectiveness: High - comprehensive multi-agent evaluation
• Data Quality: {'Excellent' if validation_success > len(consolidated_data) * 0.7 else 'Good' if validation_success > len(consolidated_data) * 0.5 else 'Needs Improvement'}
• Recommendation: {'Process optimized for quality results' if validation_success > len(consolidated_data) * 0.6 else 'Consider additional validation steps'}"""
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "generation_times": self.generation_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "report_cache_size": self.report_cache.size(),
                "template_cache_size": self.template_cache.size()
            },
            "average_generation_time": sum(self.generation_times.values()) / len(self.generation_times) if self.generation_times else 0
        }
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.report_cache.clear()
        self.template_cache.clear()
        
        if self.batch_processor:
            self.batch_processor.cleanup()
        
        gc.collect()

# Enhanced export utilities for optimized performance
class ReportExporter:
    """Optimized report export utilities with streaming and compression"""
    
    @staticmethod
    def export_to_json(state: ThreadSafeState) -> str:
        """Export to JSON with memory optimization"""
        
        try:
            # Create optimized export structure
            export_data = {
                "metadata": {
                    "job_requirements": {
                        "position_title": state["job_requirements"].position_title,
                        "company_name": state["job_requirements"].company_name,
                        "required_skills": state["job_requirements"].required_skills[:10],  # Limit for size
                        "experience_level": state["job_requirements"].experience_level
                    },
                    "processing_stats": state.get("processing_stats", {}),
                    "generated_at": datetime.now().isoformat(),
                    "optimized_export": True
                },
                "candidates": []
            }
            
            # Consolidate candidate data efficiently
            candidate_profiles = {c.get_candidate_id(): c for c in state.get("parsed_candidates", [])}
            matching_results = {r.candidate_id: r for r in state.get("matched_candidates", [])}
            research_results = {r.candidate_id: r for r in state.get("researched_candidates", [])}
            validation_results = {r.candidate_id: r for r in state.get("validated_candidates", [])}
            
            # Process only matched candidates for efficiency
            for candidate_id in matching_results.keys():
                candidate = candidate_profiles.get(candidate_id)
                if candidate:
                    # Create optimized candidate data
                    candidate_data = {
                        "basic_info": {
                            "full_name": candidate.full_name,
                            "email": candidate.email,
                            "phone": candidate.phone,
                            "location": candidate.location,
                            "linkedin_url": candidate.linkedin_url,
                            "github_url": candidate.github_url
                        },
                        "professional": {
                            "current_position": candidate.current_position,
                            "current_company": candidate.current_company,
                            "years_experience": candidate.years_experience,
                            "summary": candidate.summary[:500] if candidate.summary else ""  # Limit size
                        },
                        "skills": {
                            "technical_skills": list(candidate.technical_skills)[:15],  # Limit for size
                            "programming_languages": list(candidate.programming_languages)[:10],
                            "frameworks": list(candidate.frameworks)[:10],
                            "tools": list(candidate.tools)[:10]
                        },
                        "matching": {
                            "match_score": matching_results[candidate_id].match_score,
                            "ranking": matching_results[candidate_id].ranking,
                            "strengths": list(matching_results[candidate_id].strengths)[:5],
                            "concerns": list(matching_results[candidate_id].concerns)[:3]
                        }
                    }
                    
                    # Add research data if available (compressed)
                    research = research_results.get(candidate_id)
                    if research:
                        candidate_data["research"] = {
                            "total_results_found": research.total_results_found,
                            "platforms_searched": list(research.platforms_searched),
                            "linkedin_found": bool(research.linkedin_profile),
                            "github_found": bool(research.github_profile),
                            "github_repos": research.github_profile.get("public_repos", 0) if research.github_profile else 0
                        }
                    
                    # Add validation data if available
                    validation = validation_results.get(candidate_id)
                    if validation:
                        candidate_data["validation"] = {
                            "is_valid": validation.is_valid,
                            "confidence_score": validation.confidence_score,
                            "validation_notes": validation.validation_notes[:200] if validation.validation_notes else ""
                        }
                    
                    export_data["candidates"].append(candidate_data)
            
            return json.dumps(export_data, indent=2, default=str, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"JSON export error: {e}")
            return json.dumps({"error": f"Export failed: {str(e)}"}, indent=2)
    
    @staticmethod
    def export_to_csv_summary(consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Export to CSV with optimized data structure"""
        
        try:
            lines = []
            
            # Optimized CSV header
            headers = [
                "Rank", "Name", "Email", "Current_Position", "Current_Company",
                "Experience_Years", "Match_Score", "Top_Strengths", "Main_Concerns",
                "Validation_Status", "Confidence_Score", "LinkedIn_Found", "GitHub_Found",
                "GitHub_Repos", "Research_Platforms", "Hire_Recommendation"
            ]
            
            lines.append(",".join(headers))
            
            # Sort candidates by match score for CSV
            sorted_candidates = sorted(
                consolidated_data.items(),
                key=lambda x: x[1]["matching"].match_score,
                reverse=True
            )
            
            # Generate CSV data efficiently
            for i, (candidate_id, data) in enumerate(sorted_candidates, 1):
                candidate = data["candidate"]
                matching = data["matching"]
                research = data.get("research")
                validation = data.get("validation")
                
                # Generate hire recommendation
                score = matching.match_score
                validated = validation.is_valid if validation else False
                
                if score >= 80 and validated:
                    hire_rec = "Strong Hire"
                elif score >= 70:
                    hire_rec = "Hire"
                elif score >= 60:
                    hire_rec = "Consider"
                else:
                    hire_rec = "Pass"
                
                # Create optimized row
                row = [
                    str(i),
                    f'"{candidate.full_name}"',
                    f'"{candidate.email}"',
                    f'"{candidate.current_position}"',
                    f'"{candidate.current_company}"',
                    str(candidate.years_experience),
                    f"{matching.match_score:.1f}",
                    f'"{"; ".join(list(matching.strengths)[:2])}"',
                    f'"{"; ".join(list(matching.concerns)[:2])}"',
                    "Valid" if validation and validation.is_valid else "Review",
                    f"{validation.confidence_score:.2f}" if validation else "0.00",
                    "Yes" if research and research.linkedin_profile else "No",
                    "Yes" if research and research.github_profile else "No",
                    str(research.github_profile.get("public_repos", 0) if research and research.github_profile else 0),
                    str(len(research.platforms_searched) if research else 0),
                    hire_rec
                ]
                
                lines.append(",".join(row))
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"CSV export error: {e}")
            return f"Export Error,{str(e)}"

# Performance analytics for summarization
class SummarizationAnalytics:
    """Analytics for summarization performance"""
    
    @staticmethod
    def analyze_report_quality(candidate_reports: Dict[str, str]) -> Dict[str, Any]:
        """Analyze report quality metrics"""
        
        if not candidate_reports:
            return {"error": "No reports to analyze"}
        
        analytics = {
            "total_reports": len(candidate_reports),
            "average_length": 0,
            "completion_rate": 0,
            "content_quality_indicators": {}
        }
        
        # Analyze report lengths and content
        lengths = []
        completed_reports = 0
        
        for report in candidate_reports.values():
            if report and len(report.strip()) > 100:  # Minimum viable report
                lengths.append(len(report))
                completed_reports += 1
        
        if lengths:
            analytics["average_length"] = sum(lengths) / len(lengths)
            analytics["completion_rate"] = completed_reports / len(candidate_reports)
        
        # Content quality indicators
        analytics["content_quality_indicators"] = {
            "reports_with_analysis": len([r for r in candidate_reports.values() if "ASSESSMENT" in r]),
            "reports_with_recommendations": len([r for r in candidate_reports.values() if "RECOMMENDATION" in r]),
            "average_report_sections": sum(r.count("**") for r in candidate_reports.values()) / len(candidate_reports)
        }
        
        return analytics
    
    @staticmethod
    def generate_performance_summary(processing_stats: Dict[str, Any]) -> str:
        """Generate performance summary for summarization"""
        
        summary_lines = [
            "SUMMARIZATION PERFORMANCE SUMMARY",
            "=" * 40
        ]
        
        # Extract key metrics
        reports_generated = processing_stats.get("individual_reports_generated", 0)
        processing_time = processing_stats.get("processing_time", 0)
        reports_per_second = processing_stats.get("reports_per_second", 0)
        cache_hit_ratio = processing_stats.get("cache_hit_ratio", 0)
        
        summary_lines.extend([
            f"Reports Generated: {reports_generated}",
            f"Processing Time: {processing_time:.2f} seconds",
            f"Reports per Second: {reports_per_second:.2f}",
            f"Cache Hit Ratio: {cache_hit_ratio:.1%}",
            f"Parallel Processing: {'✅ Enabled' if processing_stats.get('parallel_processing') else '❌ Disabled'}",
            f"Template Optimization: {'✅ Enabled' if processing_stats.get('template_optimization') else '❌ Disabled'}"
        ])
        
        return "\n".join(summary_lines)

# Export optimized components
__all__ = [
    'OptimizedSummarizationAgent', 
    'ReportExporter', 
    'SummarizationAnalytics'
]