# summarization_agent_optimized.py
import asyncio
import logging
import json
import gc
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, MatchingResult, ResearchResult, ValidationResult,
    log_agent_message, update_processing_stats, LRUCache, BatchProcessor
)

logger = logging.getLogger(__name__)

class OptimizedSummarizationAgent:
    """
    High-performance summarization agent with:
    - Parallel report generation
    - Template-based optimization
    - Cached AI responses
    - Streaming report creation
    - Memory-efficient processing
    """
    
    def __init__(self, openai_api_key: str, batch_processor=None, cache=None):
        self.client = OpenAI(api_key=openai_api_key)
        self.model = "gpt-4"
        self.batch_processor = batch_processor or BatchProcessor(batch_size=4, max_workers=3)
        self.cache = cache or LRUCache(maxsize=100)
        
        # Performance settings
        self.max_concurrent_reports = 4
        self.report_cache = LRUCache(maxsize=50)
        self.template_cache = LRUCache(maxsize=20)
        
        # Performance tracking
        self.generation_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Report templates for efficiency
        self.report_templates = {
            "individual": self._get_individual_report_template(),
            "executive": self._get_executive_report_template()
        }
        
    async def create_final_reports_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Create final reports with parallel processing"""
        
        try:
            logger.info("📊 Starting high-performance report generation")
            state = log_agent_message(state, "summarization", "Starting optimized report generation", "info")
            
            start_time = time.time()
            
            # Consolidate all candidate data efficiently
            consolidated_data = self._consolidate_candidate_data_optimized(state)
            
            if not consolidated_data:
                state["errors"].append("No candidate data available for summarization")
                return state
            
            # Generate individual reports in parallel
            candidate_reports = await self._generate_individual_reports_parallel(consolidated_data)
            state["candidate_reports"] = candidate_reports
            
            # Generate executive summary
            final_report = await self._generate_executive_summary_optimized(
                consolidated_data, state["job_requirements"]
            )
            state["final_report"] = final_report
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "summarization",
                individual_reports_generated=len(candidate_reports),
                final_report_generated=bool(final_report),
                processing_time=total_time,
                reports_per_second=len(candidate_reports) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                parallel_processing=True,
                template_optimization=True
            )
            
            logger.info(f"🎯 Report generation completed in {total_time:.2f}s")
            logger.info(f"📋 Generated {len(candidate_reports)} individual reports + executive summary")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized report generation failed: {e}")
            state["errors"].append(f"Report generation failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    def _consolidate_candidate_data_optimized(self, state: ThreadSafeState) -> Dict[str, Dict[str, Any]]:
        """Optimized data consolidation with memory efficiency"""
        
        consolidated = {}
        
        # Create efficient mappings
        candidate_profiles = {c.get_candidate_id(): c for c in state.get("parsed_candidates", [])}
        matching_results = {r.candidate_id: r for r in state.get("matched_candidates", [])}
        research_results = {r.candidate_id: r for r in state.get("researched_candidates", [])}
        validation_results = {r.candidate_id: r for r in state.get("validated_candidates", [])}
        
        # Consolidate only matched candidates (memory efficient)
        for candidate_id in matching_results.keys():
            candidate = candidate_profiles.get(candidate_id)
            if candidate:
                consolidated[candidate_id] = {
                    "candidate": candidate,
                    "matching": matching_results[candidate_id],
                    "research": research_results.get(candidate_id),
                    "validation": validation_results.get(candidate_id)
                }
        
        return consolidated
    
    async def _generate_individual_reports_parallel(self, consolidated_data: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
        """Generate individual reports in parallel"""
        
        candidate_reports = {}
        
        # Process in batches for memory efficiency
        candidates = list(consolidated_data.items())
        batch_size = self.batch_processor.batch_size
        
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i + batch_size]
            
            logger.info(f"📄 Generating report batch {i//batch_size + 1}/{(len(candidates) + batch_size - 1)//batch_size}")
            
            # Create batch tasks
            batch_tasks = []
            for candidate_id, data in batch:
                task = asyncio.create_task(
                    self._generate_individual_report_cached(candidate_id, data)
                )
                batch_tasks.append((candidate_id, task))
            
            # Execute batch
            for candidate_id, task in batch_tasks:
                try:
                    report = await task
                    if report:
                        candidate_reports[candidate_id] = report
                        logger.info(f"✅ Report generated for {data['candidate'].full_name}")
                    
                except Exception as e:
                    logger.error(f"❌ Error generating report for {candidate_id}: {e}")
            
            # Brief pause between batches
            if i + batch_size < len(candidates):
                await asyncio.sleep(0.5)
            
            # Memory cleanup between batches
            gc.collect()
        
        return candidate_reports
    
    async def _generate_individual_report_cached(self, candidate_id: str, 
                                               candidate_data: Dict[str, Any]) -> Optional[str]:
        """Generate cached individual report"""
        
        # Create cache key
        candidate = candidate_data["candidate"]
        matching = candidate_data["matching"]
        validation = candidate_data.get("validation")
        
        # Create hash from key data
        key_data = f"{candidate.full_name}_{matching.match_score}_{validation.confidence_score if validation else 0}"
        cache_key = hashlib.md5(key_data.encode()).hexdigest()[:12]
        
        # Check cache
        cached_report = self.report_cache.get(cache_key)
        if cached_report:
            self.cache_hits += 1
            return cached_report
        
        self.cache_misses += 1
        
        try:
            start_time = time.time()
            
            # Use template-based generation for performance
            report = await self._generate_report_from_template(candidate_data)
            
            # Cache successful result
            if report:
                self.report_cache.put(cache_key, report)
            
            # Track timing
            generation_time = time.time() - start_time
            self.generation_times[candidate.full_name] = generation_time
            
            return report
            
        except Exception as e:
            logger.error(f"Individual report generation error: {e}")
            return f"Error generating report for {candidate.full_name}: {str(e)}"
    
    async def _generate_report_from_template(self, candidate_data: Dict[str, Any]) -> str:
        """Generate report using optimized template"""
        
        candidate = candidate_data["candidate"]
        matching = candidate_data["matching"]
        research = candidate_data.get("research")
        validation = candidate_data.get("validation")
        
        # Create metadata header efficiently
        metadata_header = self._create_metadata_header_fast(candidate, matching, validation)
        
        # Use template for consistent structure
        template = self.report_templates["individual"]
        
        # Fill template with data
        report_data = self._prepare_template_data(candidate, matching, research, validation)
        
        # Generate AI content only for analysis section
        analysis_section = await self._generate_analysis_section_optimized(report_data)
        
        # Combine all sections
        full_report = template.format(
            metadata_header=metadata_header,
            basic_info=self._format_basic_info(candidate),
            professional_summary=self._format_professional_summary(candidate),
            skills_section=self._format_skills_section(candidate),
            experience_section=self._format_experience_section(candidate),
            matching_section=self._format_matching_section(matching),
            research_section=self._format_research_section(research),
            validation_section=self._format_validation_section(validation),
            analysis_section=analysis_section,
            recommendations=self._generate_recommendations_fast(matching, validation)
        )
        
        return full_report
    
    def _get_individual_report_template(self) -> str:
        """Get optimized individual report template"""
        
        return """
{metadata_header}

{basic_info}

{professional_summary}

{skills_section}

{experience_section}

{matching_section}

{research_section}

{validation_section}

{analysis_section}

{recommendations}
"""
    
    def _get_executive_report_template(self) -> str:
        """Get executive report template"""
        
        return """
{header}

{overview}

{top_candidates}

{market_analysis}

{recommendations}

{process_insights}
"""
    
    def _prepare_template_data(self, candidate: CandidateProfile, matching: MatchingResult,
                             research: Optional[ResearchResult], validation: Optional[ValidationResult]) -> Dict[str, Any]:
        """Prepare data for template filling"""
        
        return {
            "candidate_name": candidate.full_name,
            "current_role": f"{candidate.current_position} at {candidate.current_company}",
            "experience_years": candidate.years_experience,
            "match_score": matching.match_score,
            "ranking": matching.ranking,
            "key_strengths": list(matching.strengths)[:3],
            "main_concerns": list(matching.concerns)[:2],
            "validation_status": validation.is_valid if validation else False,
            "confidence_score": validation.confidence_score if validation else 0.0,
            "research_platforms": len(research.platforms_searched) if research else 0,
            "total_research_results": research.total_results_found if research else 0
        }
    
    async def _generate_analysis_section_optimized(self, report_data: Dict[str, Any]) -> str:
        """Generate optimized analysis section using AI"""
        
        try:
            # Create concise prompt for fast processing
            prompt = f"""Generate a brief professional analysis for this candidate:

Name: {report_data['candidate_name']}
Role: {report_data['current_role']}
Experience: {report_data['experience_years']} years
Match Score: {report_data['match_score']:.1f}/100
Ranking: #{report_data['ranking']}
Validation: {'✅ Validated' if report_data['validation_status'] else '⚠️ Review Needed'}

Key Strengths: {', '.join(report_data['key_strengths'])}
Concerns: {', '.join(report_data['main_concerns'])}

Provide a 2-3 sentence professional assessment focusing on hiring recommendation."""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=300  # Reduced for performance
            )
            
            return f"**PROFESSIONAL ASSESSMENT:**\n{response.choices[0].message.content.strip()}"
            
        except Exception as e:
            logger.error(f"Analysis section generation error: {e}")
            score = report_data['match_score']
            return f"**PROFESSIONAL ASSESSMENT:**\nCandidate shows {score:.1f}% alignment with job requirements. {'Strong consideration recommended.' if score > 75 else 'Further evaluation needed.' if score > 60 else 'Limited alignment with requirements.'}"
    
    def _create_metadata_header_fast(self, candidate: CandidateProfile, matching: MatchingResult,
                                   validation: Optional[ValidationResult]) -> str:
        """Create metadata header efficiently"""
        
        validation_status = "✅ Validated" if validation and validation.is_valid else "⚠️ Needs Review"
        confidence_text = f"{validation.confidence_score:.2f}" if validation else "N/A"
        
        return f"""CANDIDATE EVALUATION REPORT
{'=' * 60}

Candidate: {candidate.full_name}
Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Match Score: {matching.match_score:.1f}/100
Ranking: #{matching.ranking}
Validation Status: {validation_status}
Confidence Level: {confidence_text}

{'=' * 60}"""
    
    def _format_basic_info(self, candidate: CandidateProfile) -> str:
        """Format basic information section"""
        
        return f"""
**BASIC INFORMATION:**
• Name: {candidate.full_name}
• Email: {candidate.email or 'Not provided'}
• Phone: {candidate.phone or 'Not provided'}
• Location: {candidate.location or 'Not provided'}
• LinkedIn: {candidate.linkedin_url or 'Not provided'}
• GitHub: {candidate.github_url or 'Not provided'}"""
    
    def _format_professional_summary(self, candidate: CandidateProfile) -> str:
        """Format professional summary section"""
        
        return f"""
**PROFESSIONAL SUMMARY:**
• Current Position: {candidate.current_position or 'Not specified'}
• Current Company: {candidate.current_company or 'Not specified'}
• Total Experience: {candidate.years_experience} years
• Summary: {candidate.summary[:300] + '...' if len(candidate.summary) > 300 else candidate.summary or 'Not provided'}"""
    
    def _format_skills_section(self, candidate: CandidateProfile) -> str:
        """Format skills section efficiently"""
        
        skills_text = f"""
**TECHNICAL COMPETENCY:**
• Programming Languages: {', '.join(list(candidate.programming_languages)[:10]) or 'Not specified'}
• Technical Skills: {', '.join(list(candidate.technical_skills)[:10]) or 'Not specified'}
• Frameworks: {', '.join(list(candidate.frameworks)[:8]) or 'Not specified'}
• Tools: {', '.join(list(candidate.tools)[:8]) or 'Not specified'}"""
        
        return skills_text
    
    def _format_experience_section(self, candidate: CandidateProfile) -> str:
        """Format experience section efficiently"""
        
        if not candidate.work_experience:
            return "\n**WORK EXPERIENCE:**\n• No detailed work experience provided"
        
        exp_lines = ["\n**WORK EXPERIENCE:**"]
        
        # Show top 3 experiences
        for i, exp in enumerate(list(candidate.work_experience)[:3]):
            position = exp.get('position', 'Position not specified')
            company = exp.get('company', 'Company not specified')
            duration = exp.get('duration', 'Duration not specified')
            
            exp_lines.append(f"• {position} at {company} ({duration})")
        
        if len(candidate.work_experience) > 3:
            exp_lines.append(f"• ... and {len(candidate.work_experience) - 3} more positions")
        
        return '\n'.join(exp_lines)
    
    def _format_matching_section(self, matching: MatchingResult) -> str:
        """Format matching analysis section"""
        
        return f"""
**MATCHING ANALYSIS:**
• Overall Match Score: {matching.match_score:.1f}/100
• Ranking: #{matching.ranking}
• Key Strengths: {', '.join(list(matching.strengths)[:3]) or 'None specified'}
• Areas of Concern: {', '.join(list(matching.concerns)[:2]) or 'None identified'}
• Match Reasons: {', '.join(list(matching.match_reasons)[:3]) or 'Not specified'}"""
    
    def _format_research_section(self, research: Optional[ResearchResult]) -> str:
        """Format research findings section"""
        
        if not research:
            return "\n**RESEARCH FINDINGS:**\n• No research data available"
        
        research_text = f"""
**RESEARCH FINDINGS:**
• Total Results Found: {research.total_results_found}
• Platforms Searched: {', '.join(research.platforms_searched)}
• LinkedIn Profile: {'✅ Found' if research.linkedin_profile else '❌ Not found'}
• GitHub Profile: {'✅ Found' if research.github_profile else '❌ Not found'}"""
        
        if research.github_profile:
            github = research.github_profile
            research_text += f"""
• GitHub Stats: {github.get('public_repos', 0)} repos, {github.get('total_stars', 0)} stars"""
        
        if research.research_summary:
            research_text += f"\n• Summary: {research.research_summary}"
        
        return research_text
    
    def _format_validation_section(self, validation: Optional[ValidationResult]) -> str:
        """Format validation section"""
        
        if not validation:
            return "\n**VALIDATION STATUS:**\n• No validation performed"
        
        status_icon = "✅" if validation.is_valid else "⚠️"
        
        validation_text = f"""
**VALIDATION STATUS:**
• Status: {status_icon} {'Validated' if validation.is_valid else 'Needs Review'}
• Confidence Score: {validation.confidence_score:.2f}
• Validation Notes: {validation.validation_notes or 'No additional notes'}"""
        
        if validation.discrepancies:
            validation_text += f"\n• Discrepancies: {', '.join(validation.discrepancies)}"
        
        return validation_text
    
    def _generate_recommendations_fast(self, matching: MatchingResult, 
                                     validation: Optional[ValidationResult]) -> str:
        """Generate fast recommendations"""
        
        score = matching.match_score
        validated = validation.is_valid if validation else False
        
        if score >= 80 and validated:
            recommendation = "**STRONG HIRE** - Excellent fit for the role"
        elif score >= 70 and validated:
            recommendation = "**HIRE** - Good candidate with strong potential"
        elif score >= 60:
            recommendation = "**CONSIDER** - Promising candidate, needs further evaluation"
        elif score >= 50:
            recommendation = "**MAYBE** - Some alignment but significant gaps"
        else:
            recommendation = "**PASS** - Limited alignment with requirements"
        
        return f"""
**HIRING RECOMMENDATION:**
{recommendation}

**NEXT STEPS:**
• Interview focus areas: {', '.join(list(matching.concerns)[:2]) if matching.concerns else 'General competency assessment'}
• Reference check priority: {'High' if validated else 'Critical - identity verification needed'}
• Technical assessment: {'Recommended' if score < 80 else 'Optional - strong technical background evident'}"""
    
    async def _generate_executive_summary_optimized(self, consolidated_data: Dict[str, Dict[str, Any]], 
                                                  job_requirements) -> str:
        """Generate optimized executive summary"""
        
        try:
            # Sort candidates by match score
            sorted_candidates = sorted(
                consolidated_data.items(),
                key=lambda x: x[1]["matching"].match_score,
                reverse=True
            )
            
            # Create summary data efficiently
            top_5_candidates = []
            for candidate_id, data in sorted_candidates[:5]:
                candidate = data["candidate"]
                matching = data["matching"]
                validation = data.get("validation")
                
                top_5_candidates.append({
                    "name": candidate.full_name,
                    "score": matching.match_score,
                    "ranking": matching.ranking,
                    "current_role": f"{candidate.current_position} at {candidate.current_company}",
                    "validated": validation.is_valid if validation else False
                })
            
            # Generate executive summary using template
            exec_header = self._create_executive_header(consolidated_data, job_requirements)
            overview = self._create_overview_section(consolidated_data)
            top_candidates_section = self._format_top_candidates(top_5_candidates)
            market_analysis = self._create_market_analysis(consolidated_data)
            recommendations = self._create_executive_recommendations(sorted_candidates)
            
            template = self.report_templates["executive"]
            
            executive_summary = template.format(
                header=exec_header,
                overview=overview,
                top_candidates=top_candidates_section,
                market_analysis=market_analysis,
                recommendations=recommendations,
                process_insights=self._create_process_insights(consolidated_data)
            )
            
            return executive_summary
            
        except Exception as e:
            logger.error(f"Executive summary generation error: {e}")
            return f"Error generating executive summary: {str(e)}"
    
    def _create_executive_header(self, consolidated_data: Dict[str, Dict[str, Any]], job_requirements) -> str:
        """Create executive summary header"""
        
        total_candidates = len(consolidated_data)
        validated_candidates = len([d for d in consolidated_data.values() 
                                  if d.get("validation") and d["validation"].is_valid])
        avg_match_score = sum(d["matching"].match_score for d in consolidated_data.values()) / total_candidates if total_candidates > 0 else 0
        
        return f"""EXECUTIVE HIRING REPORT
{'=' * 80}

Position: {job_requirements.position_title}
Company: {job_requirements.company_name}
Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 EVALUATION STATISTICS:
   • Total Candidates Evaluated: {total_candidates}
   • Candidates Validated: {validated_candidates}
   • Average Match Score: {avg_match_score:.1f}/100
   • AI-Powered Multi-Agent Analysis

{'=' * 80}"""
    
    def _create_overview_section(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create overview section"""
        
        total = len(consolidated_data)
        strong_candidates = len([d for d in consolidated_data.values() if d["matching"].match_score >= 80])
        good_candidates = len([d for d in consolidated_data.values() if 70 <= d["matching"].match_score < 80])
        
        return f"""
**SEARCH OVERVIEW:**
• Total candidates in final evaluation: {total}
• Strong candidates (80+ score): {strong_candidates}
• Good candidates (70-79 score): {good_candidates}
• Evaluation methodology: Multi-agent AI analysis with research validation"""
    
    def _format_top_candidates(self, top_candidates: List[Dict[str, Any]]) -> str:
        """Format top candidates section"""
        
        section = ["\n**TOP CANDIDATES RANKING:**"]
        
        for i, candidate in enumerate(top_candidates, 1):
            validation_icon = "✅" if candidate["validated"] else "⚠️"
            section.append(
                f"{i}. {candidate['name']} - {candidate['score']:.1f}/100 {validation_icon}\n"
                f"   Current: {candidate['current_role']}"
            )
        
        return '\n'.join(section)
    
    def _create_market_analysis(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create market analysis section"""
        
        skills_frequency = {}
        experience_levels = {'0-2': 0, '3-5': 0, '6-10': 0, '10+': 0}
        
        for data in consolidated_data.values():
            candidate = data["candidate"]
            
            # Analyze skills
            for skill in list(candidate.technical_skills) + list(candidate.programming_languages):
                skills_frequency[skill] = skills_frequency.get(skill, 0) + 1
            
            # Analyze experience
            years = candidate.years_experience
            if years <= 2:
                experience_levels['0-2'] += 1
            elif years <= 5:
                experience_levels['3-5'] += 1
            elif years <= 10:
                experience_levels['6-10'] += 1
            else:
                experience_levels['10+'] += 1
        
        top_skills = sorted(skills_frequency.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return f"""
**MARKET ANALYSIS:**
• Experience Distribution: {dict(experience_levels)}
• Top Skills Found: {', '.join([f"{skill} ({count})" for skill, count in top_skills])}
• Candidate Pool Quality: {'Excellent' if len([d for d in consolidated_data.values() if d["matching"].match_score >= 70]) >= 3 else 'Good' if len([d for d in consolidated_data.values() if d["matching"].match_score >= 60]) >= 2 else 'Limited'}"""
    
    def _create_executive_recommendations(self, sorted_candidates: List[Tuple]) -> str:
        """Create executive recommendations"""
        
        if not sorted_candidates:
            return "\n**RECOMMENDATIONS:**\n• No qualified candidates found"
        
        top_candidate = sorted_candidates[0][1]
        top_score = top_candidate["matching"].match_score
        
        if top_score >= 80:
            urgency = "HIGH PRIORITY"
            action = "Schedule interviews immediately"
        elif top_score >= 70:
            urgency = "RECOMMENDED"
            action = "Proceed with interview process"
        else:
            urgency = "CONSIDER EXPANDING SEARCH"
            action = "Current pool may not meet requirements"
        
        return f"""
**HIRING RECOMMENDATIONS:**
• Immediate Action: {urgency}
• Next Steps: {action}
• Interview Priority: Top 3 candidates
• Timeline: {'Fast-track recommended' if top_score >= 75 else 'Standard process'}
• Budget Considerations: {'Competitive offer may be required' if top_score >= 85 else 'Standard range appropriate'}"""
    
    def _create_process_insights(self, consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Create process insights section"""
        
        research_success = len([d for d in consolidated_data.values() 
                              if d.get("research") and d["research"].total_results_found > 0])
        
        validation_success = len([d for d in consolidated_data.values() 
                                if d.get("validation") and d["validation"].is_valid])
        
        return f"""
**PROCESS INSIGHTS:**
• Research Success Rate: {research_success}/{len(consolidated_data)} candidates
• Validation Success Rate: {validation_success}/{len(consolidated_data)} candidates
• AI Analysis Effectiveness: High - comprehensive multi-agent evaluation
• Data Quality: {'Excellent' if validation_success > len(consolidated_data) * 0.7 else 'Good' if validation_success > len(consolidated_data) * 0.5 else 'Needs Improvement'}
• Recommendation: {'Process optimized for quality results' if validation_success > len(consolidated_data) * 0.6 else 'Consider additional validation steps'}"""
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "generation_times": self.generation_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "report_cache_size": self.report_cache.size(),
                "template_cache_size": self.template_cache.size()
            },
            "average_generation_time": sum(self.generation_times.values()) / len(self.generation_times) if self.generation_times else 0
        }
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.report_cache.clear()
        self.template_cache.clear()
        
        if self.batch_processor:
            self.batch_processor.cleanup()
        
        gc.collect()

# Enhanced export utilities for optimized performance
class ReportExporter:
    """Optimized report export utilities with streaming and compression"""
    
    @staticmethod
    def export_to_json(state: ThreadSafeState) -> str:
        """Export to JSON with memory optimization"""
        
        try:
            # Create optimized export structure
            export_data = {
                "metadata": {
                    "job_requirements": {
                        "position_title": state["job_requirements"].position_title,
                        "company_name": state["job_requirements"].company_name,
                        "required_skills": state["job_requirements"].required_skills[:10],  # Limit for size
                        "experience_level": state["job_requirements"].experience_level
                    },
                    "processing_stats": state.get("processing_stats", {}),
                    "generated_at": datetime.now().isoformat(),
                    "optimized_export": True
                },
                "candidates": []
            }
            
            # Consolidate candidate data efficiently
            candidate_profiles = {c.get_candidate_id(): c for c in state.get("parsed_candidates", [])}
            matching_results = {r.candidate_id: r for r in state.get("matched_candidates", [])}
            research_results = {r.candidate_id: r for r in state.get("researched_candidates", [])}
            validation_results = {r.candidate_id: r for r in state.get("validated_candidates", [])}
            
            # Process only matched candidates for efficiency
            for candidate_id in matching_results.keys():
                candidate = candidate_profiles.get(candidate_id)
                if candidate:
                    # Create optimized candidate data
                    candidate_data = {
                        "basic_info": {
                            "full_name": candidate.full_name,
                            "email": candidate.email,
                            "phone": candidate.phone,
                            "location": candidate.location,
                            "linkedin_url": candidate.linkedin_url,
                            "github_url": candidate.github_url
                        },
                        "professional": {
                            "current_position": candidate.current_position,
                            "current_company": candidate.current_company,
                            "years_experience": candidate.years_experience,
                            "summary": candidate.summary[:500] if candidate.summary else ""  # Limit size
                        },
                        "skills": {
                            "technical_skills": list(candidate.technical_skills)[:15],  # Limit for size
                            "programming_languages": list(candidate.programming_languages)[:10],
                            "frameworks": list(candidate.frameworks)[:10],
                            "tools": list(candidate.tools)[:10]
                        },
                        "matching": {
                            "match_score": matching_results[candidate_id].match_score,
                            "ranking": matching_results[candidate_id].ranking,
                            "strengths": list(matching_results[candidate_id].strengths)[:5],
                            "concerns": list(matching_results[candidate_id].concerns)[:3]
                        }
                    }
                    
                    # Add research data if available (compressed)
                    research = research_results.get(candidate_id)
                    if research:
                        candidate_data["research"] = {
                            "total_results_found": research.total_results_found,
                            "platforms_searched": list(research.platforms_searched),
                            "linkedin_found": bool(research.linkedin_profile),
                            "github_found": bool(research.github_profile),
                            "github_repos": research.github_profile.get("public_repos", 0) if research.github_profile else 0
                        }
                    
                    # Add validation data if available
                    validation = validation_results.get(candidate_id)
                    if validation:
                        candidate_data["validation"] = {
                            "is_valid": validation.is_valid,
                            "confidence_score": validation.confidence_score,
                            "validation_notes": validation.validation_notes[:200] if validation.validation_notes else ""
                        }
                    
                    export_data["candidates"].append(candidate_data)
            
            return json.dumps(export_data, indent=2, default=str, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"JSON export error: {e}")
            return json.dumps({"error": f"Export failed: {str(e)}"}, indent=2)
    
    @staticmethod
    def export_to_csv_summary(consolidated_data: Dict[str, Dict[str, Any]]) -> str:
        """Export to CSV with optimized data structure"""
        
        try:
            lines = []
            
            # Optimized CSV header
            headers = [
                "Rank", "Name", "Email", "Current_Position", "Current_Company",
                "Experience_Years", "Match_Score", "Top_Strengths", "Main_Concerns",
                "Validation_Status", "Confidence_Score", "LinkedIn_Found", "GitHub_Found",
                "GitHub_Repos", "Research_Platforms", "Hire_Recommendation"
            ]
            
            lines.append(",".join(headers))
            
            # Sort candidates by match score for CSV
            sorted_candidates = sorted(
                consolidated_data.items(),
                key=lambda x: x[1]["matching"].match_score,
                reverse=True
            )
            
            # Generate CSV data efficiently
            for i, (candidate_id, data) in enumerate(sorted_candidates, 1):
                candidate = data["candidate"]
                matching = data["matching"]
                research = data.get("research")
                validation = data.get("validation")
                
                # Generate hire recommendation
                score = matching.match_score
                validated = validation.is_valid if validation else False
                
                if score >= 80 and validated:
                    hire_rec = "Strong Hire"
                elif score >= 70:
                    hire_rec = "Hire"
                elif score >= 60:
                    hire_rec = "Consider"
                else:
                    hire_rec = "Pass"
                
                # Create optimized row
                row = [
                    str(i),
                    f'"{candidate.full_name}"',
                    f'"{candidate.email}"',
                    f'"{candidate.current_position}"',
                    f'"{candidate.current_company}"',
                    str(candidate.years_experience),
                    f"{matching.match_score:.1f}",
                    f'"{"; ".join(list(matching.strengths)[:2])}"',
                    f'"{"; ".join(list(matching.concerns)[:2])}"',
                    "Valid" if validation and validation.is_valid else "Review",
                    f"{validation.confidence_score:.2f}" if validation else "0.00",
                    "Yes" if research and research.linkedin_profile else "No",
                    "Yes" if research and research.github_profile else "No",
                    str(research.github_profile.get("public_repos", 0) if research and research.github_profile else 0),
                    str(len(research.platforms_searched) if research else 0),
                    hire_rec
                ]
                
                lines.append(",".join(row))
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"CSV export error: {e}")
            return f"Export Error,{str(e)}"

# Performance analytics for summarization
class SummarizationAnalytics:
    """Analytics for summarization performance"""
    
    @staticmethod
    def analyze_report_quality(candidate_reports: Dict[str, str]) -> Dict[str, Any]:
        """Analyze report quality metrics"""
        
        if not candidate_reports:
            return {"error": "No reports to analyze"}
        
        analytics = {
            "total_reports": len(candidate_reports),
            "average_length": 0,
            "completion_rate": 0,
            "content_quality_indicators": {}
        }
        
        # Analyze report lengths and content
        lengths = []
        completed_reports = 0
        
        for report in candidate_reports.values():
            if report and len(report.strip()) > 100:  # Minimum viable report
                lengths.append(len(report))
                completed_reports += 1
        
        if lengths:
            analytics["average_length"] = sum(lengths) / len(lengths)
            analytics["completion_rate"] = completed_reports / len(candidate_reports)
        
        # Content quality indicators
        analytics["content_quality_indicators"] = {
            "reports_with_analysis": len([r for r in candidate_reports.values() if "ASSESSMENT" in r]),
            "reports_with_recommendations": len([r for r in candidate_reports.values() if "RECOMMENDATION" in r]),
            "average_report_sections": sum(r.count("**") for r in candidate_reports.values()) / len(candidate_reports)
        }
        
        return analytics
    
    @staticmethod
    def generate_performance_summary(processing_stats: Dict[str, Any]) -> str:
        """Generate performance summary for summarization"""
        
        summary_lines = [
            "SUMMARIZATION PERFORMANCE SUMMARY",
            "=" * 40
        ]
        
        # Extract key metrics
        reports_generated = processing_stats.get("individual_reports_generated", 0)
        processing_time = processing_stats.get("processing_time", 0)
        reports_per_second = processing_stats.get("reports_per_second", 0)
        cache_hit_ratio = processing_stats.get("cache_hit_ratio", 0)
        
        summary_lines.extend([
            f"Reports Generated: {reports_generated}",
            f"Processing Time: {processing_time:.2f} seconds",
            f"Reports per Second: {reports_per_second:.2f}",
            f"Cache Hit Ratio: {cache_hit_ratio:.1%}",
            f"Parallel Processing: {'✅ Enabled' if processing_stats.get('parallel_processing') else '❌ Disabled'}",
            f"Template Optimization: {'✅ Enabled' if processing_stats.get('template_optimization') else '❌ Disabled'}"
        ])
        
        return "\n".join(summary_lines)

# Export optimized components
__all__ = [
    'OptimizedSummarizationAgent', 
    'ReportExporter', 
    'SummarizationAnalytics'
]