# workflow_graph.py
import asyncio
import logging
from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from state_management import AgentState, validate_state_transition

logger = logging.getLogger(__name__)

class ResumeAnalysisWorkflow:
    """
    LangGraph-based workflow implementation for the multi-agent resume analysis system
    """
    
    def __init__(self, openai_api_key: str, serp_api_key: str = None, tavily_api_key: str = None, github_token: str = None):
        self.openai_api_key = openai_api_key
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.github_token = github_token
        self.workflow = None
        self._setup_workflow()
    
    def _setup_workflow(self):
        """Setup the LangGraph workflow"""
        
        # Create the workflow graph
        workflow = StateGraph(AgentState)
        
        # Add nodes for each agent
        workflow.add_node("orchestrator", self._orchestrator_node)
        workflow.add_node("resume_analysis", self._resume_analysis_node)
        workflow.add_node("matching", self._matching_node)
        workflow.add_node("research", self._research_node)
        workflow.add_node("validation", self._validation_node)
        workflow.add_node("summarization", self._summarization_node)
        
        # Define the workflow edges
        workflow.set_entry_point("orchestrator")
        
        workflow.add_edge("orchestrator", "resume_analysis")
        workflow.add_edge("resume_analysis", "matching")
        workflow.add_edge("matching", "research")
        workflow.add_edge("research", "validation")
        workflow.add_edge("validation", "summarization")
        workflow.add_edge("summarization", END)
        
        # Add conditional edges for error handling
        workflow.add_conditional_edges(
            "resume_analysis",
            self._should_continue_after_resume_analysis,
            {
                "continue": "matching",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "matching",
            self._should_continue_after_matching,
            {
                "continue": "research",
                "skip_research": "validation",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "research",
            self._should_continue_after_research,
            {
                "continue": "validation",
                "error": END
            }
        )
        
        workflow.add_conditional_edges(
            "validation",
            self._should_continue_after_validation,
            {
                "continue": "summarization",
                "retry_research": "research",
                "error": END
            }
        )
        
        self.workflow = workflow.compile()
    
    async def run_workflow(self, initial_state: AgentState) -> AgentState:
        """Run the complete workflow"""
        
        try:
            logger.info("🚀 Starting LangGraph workflow execution")
            
            # Execute the workflow
            result = await self.workflow.ainvoke(initial_state)
            
            logger.info("✅ LangGraph workflow completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"❌ LangGraph workflow failed: {e}")
            initial_state["errors"].append(f"Workflow execution failed: {str(e)}")
            return initial_state
    
    async def _orchestrator_node(self, state: AgentState) -> AgentState:
        """Orchestrator node - workflow coordination"""
        
        try:
            logger.info("🎼 Orchestrator: Initializing workflow")
            
            from agents.orchestrator_agent import OrchestratorAgent
            
            # Initialize orchestrator
            orchestrator = OrchestratorAgent(
                openai_api_key=self.openai_api_key,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                github_token=self.github_token
            )
            
            # Initialize workflow
            state = await orchestrator._initialize_workflow(state)
            
            # Set next step
            state["current_step"] = "resume_analysis"
            state["next_agent"] = "resume_analysis"
            
            logger.info("✅ Orchestrator: Workflow initialized")
            return state
            
        except Exception as e:
            logger.error(f"❌ Orchestrator node error: {e}")
            state["errors"].append(f"Orchestrator error: {str(e)}")
            return state
    
    async def _resume_analysis_node(self, state: AgentState) -> AgentState:
        """Resume analysis node"""
        
        try:
            logger.info("📄 Resume Analysis: Starting CV processing")
            
            from agents.resume_analysis_agent import ResumeAnalysisAgent
            
            agent = ResumeAnalysisAgent(self.openai_api_key)
            state = await agent.analyze_resumes(state)
            
            state["current_step"] = "matching"
            
            logger.info(f"✅ Resume Analysis: Processed {len(state['parsed_candidates'])} candidates")
            return state
            
        except Exception as e:
            logger.error(f"❌ Resume analysis node error: {e}")
            state["errors"].append(f"Resume analysis error: {str(e)}")
            return state
    
    async def _matching_node(self, state: AgentState) -> AgentState:
        """Matching node"""
        
        try:
            logger.info("🎯 Matching: Starting candidate matching")
            
            from agents.matching_agent_enhanced import EnhancedMatchingAgent
            
            agent = EnhancedMatchingAgent(self.openai_api_key)
            state = await agent.match_candidates(state)
            
            state["current_step"] = "research"
            
            logger.info(f"✅ Matching: Found {len(state['matched_candidates'])} top candidates")
            return state
            
        except Exception as e:
            logger.error(f"❌ Matching node error: {e}")
            state["errors"].append(f"Matching error: {str(e)}")
            return state
    
    async def _research_node(self, state: AgentState) -> AgentState:
        """Research node"""
        
        try:
            logger.info("🔍 Research: Starting deep candidate research")
            
            from agents.research_agent_enhanced import EnhancedResearchAgent
            
            agent = EnhancedResearchAgent(
                openai_api_key=self.openai_api_key,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                github_token=self.github_token
            )
            state = await agent.research_candidates(state)
            
            state["current_step"] = "validation"
            
            logger.info(f"✅ Research: Completed research for {len(state['researched_candidates'])} candidates")
            return state
            
        except Exception as e:
            logger.error(f"❌ Research node error: {e}")
            state["errors"].append(f"Research error: {str(e)}")
            # Continue with validation even if research fails
            state["current_step"] = "validation"
            return state
    
    async def _validation_node(self, state: AgentState) -> AgentState:
        """Validation node"""
        
        try:
            logger.info("✅ Validation: Starting candidate validation")
            
            from agents.validation_agent import ValidationAgent
            
            agent = ValidationAgent(self.openai_api_key)
            state = await agent.validate_candidates(state)
            
            state["current_step"] = "summarization"
            
            logger.info(f"✅ Validation: Validated {len(state['validated_candidates'])} candidates")
            return state
            
        except Exception as e:
            logger.error(f"❌ Validation node error: {e}")
            state["errors"].append(f"Validation error: {str(e)}")
            # Continue with summarization even if validation fails
            state["current_step"] = "summarization"
            return state
    
    async def _summarization_node(self, state: AgentState) -> AgentState:
        """Summarization node"""
        
        try:
            logger.info("📊 Summarization: Generating final reports")
            
            from agents.summarization_agent import SummarizationAgent
            
            agent = SummarizationAgent(self.openai_api_key)
            state = await agent.create_final_reports(state)
            
            state["current_step"] = "completed"
            
            logger.info(f"✅ Summarization: Generated {len(state['candidate_reports'])} reports")
            return state
            
        except Exception as e:
            logger.error(f"❌ Summarization node error: {e}")
            state["errors"].append(f"Summarization error: {str(e)}")
            return state
    
    def _should_continue_after_resume_analysis(self, state: AgentState) -> str:
        """Determine next step after resume analysis"""
        
        if state["errors"]:
            logger.error("Resume analysis failed, ending workflow")
            return "error"
        
        if not state["parsed_candidates"]:
            logger.error("No candidates parsed, ending workflow")
            state["errors"].append("No candidates were successfully parsed")
            return "error"
        
        return "continue"
    
    def _should_continue_after_matching(self, state: AgentState) -> str:
        """Determine next step after matching"""
        
        if state["errors"]:
            logger.error("Matching failed, ending workflow")
            return "error"
        
        if not state["matched_candidates"]:
            logger.error("No candidates matched, ending workflow")
            state["errors"].append("No candidates matched the job requirements")
            return "error"
        
        # Check if research should be skipped (e.g., no API keys)
        if not self.serp_api_key and not self.tavily_api_key:
            logger.warning("No research API keys provided, skipping research")
            return "skip_research"
        
        return "continue"
    
    def _should_continue_after_research(self, state: AgentState) -> str:
        """Determine next step after research"""
        
        # Research errors are not fatal - continue with validation
        return "continue"
    
    def _should_continue_after_validation(self, state: AgentState) -> str:
        """Determine next step after validation"""
        
        # Check if any candidates failed validation and need re-research
        failed_validations = [
            v for v in state["validated_candidates"]
            if not v.is_valid and v.confidence_score < 0.3
        ]
        
        if failed_validations and len(failed_validations) < len(state["validated_candidates"]) / 2:
            # If less than half failed validation, retry research for failed ones
            logger.warning(f"Retrying research for {len(failed_validations)} failed validations")
            return "retry_research"
        
        return "continue"


# Alternative workflow using CrewAI-style approach
class CrewAIStyleWorkflow:
    """
    Alternative implementation using CrewAI-style agent coordination
    """
    
    def __init__(self, openai_api_key: str, serp_api_key: str = None, tavily_api_key: str = None, github_token: str = None):
        self.openai_api_key = openai_api_key
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.github_token = github_token
        self.agents = {}
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize all agents"""
        
        from agents.resume_analysis_agent import ResumeAnalysisAgent
        from agents.matching_agent_enhanced import EnhancedMatchingAgent
        from agents.research_agent_enhanced import EnhancedResearchAgent
        from agents.validation_agent import ValidationAgent
        from agents.summarization_agent import SummarizationAgent
        
        self.agents = {
            "resume_analyst": ResumeAnalysisAgent(self.openai_api_key),
            "matcher": EnhancedMatchingAgent(self.openai_api_key),
            "researcher": EnhancedResearchAgent(
                openai_api_key=self.openai_api_key,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                github_token=self.github_token
            ),
            "validator": ValidationAgent(self.openai_api_key),
            "summarizer": SummarizationAgent(self.openai_api_key)
        }
    
    async def execute_crew_workflow(self, initial_state: AgentState) -> AgentState:
        """Execute workflow using crew-style coordination"""
        
        try:
            logger.info("👥 Starting CrewAI-style workflow")
            
            # Task 1: Resume Analysis
            logger.info("📋 Task 1: Resume Analysis")
            state = await self.agents["resume_analyst"].analyze_resumes(initial_state)
            
            if state["errors"] or not state["parsed_candidates"]:
                logger.error("Resume analysis failed, aborting workflow")
                return state
            
            # Task 2: Candidate Matching
            logger.info("📋 Task 2: Candidate Matching")
            state = await self.agents["matcher"].match_candidates(state)
            
            if state["errors"] or not state["matched_candidates"]:
                logger.error("Matching failed, aborting workflow")
                return state
            
            # Task 3: Deep Research
            logger.info("📋 Task 3: Deep Research")
            state = await self.agents["researcher"].research_candidates(state)
            
            # Task 4: Validation (continue even if research fails)
            logger.info("📋 Task 4: Validation")
            state = await self.agents["validator"].validate_candidates(state)
            
            # Task 5: Summarization
            logger.info("📋 Task 5: Report Generation")
            state = await self.agents["summarizer"].create_final_reports(state)
            
            logger.info("✅ CrewAI-style workflow completed")
            return state
            
        except Exception as e:
            logger.error(f"❌ CrewAI workflow failed: {e}")
            initial_state["errors"].append(f"Workflow execution failed: {str(e)}")
            return initial_state


# Autogen-style workflow implementation
class AutoGenStyleWorkflow:
    """
    Alternative implementation using AutoGen-style multi-agent conversation
    """
    
    def __init__(self, openai_api_key: str):
        self.openai_api_key = openai_api_key
        self.conversation_history = []
    
    async def execute_autogen_workflow(self, initial_state: AgentState) -> AgentState:
        """Execute workflow using AutoGen-style agent conversation"""
        
        try:
            logger.info("💬 Starting AutoGen-style workflow")
            
            # Simulate agent conversation flow
            messages = [
                {"agent": "user", "message": f"Analyze {len(initial_state['uploaded_files'])} resumes for {initial_state['job_requirements'].position_title}"},
                {"agent": "resume_analyst", "message": "I'll analyze the resumes and extract candidate information"},
                {"agent": "matcher", "message": "Once analysis is complete, I'll match candidates to job requirements"},
                {"agent": "researcher", "message": "I'll then research the top candidates online"},
                {"agent": "validator", "message": "I'll validate the research findings for accuracy"},
                {"agent": "summarizer", "message": "Finally, I'll create comprehensive reports"}
            ]
            
            # Execute each agent in sequence with conversation context
            state = initial_state
            
            # Resume Analysis
            from agents.resume_analysis_agent import ResumeAnalysisAgent
            agent = ResumeAnalysisAgent(self.openai_api_key)
            state = await agent.analyze_resumes(state)
            
            self.conversation_history.append({
                "agent": "resume_analyst",
                "action": "analyze_resumes",
                "result": f"Processed {len(state['parsed_candidates'])} candidates"
            })
            
            # Matching
            from agents.matching_agent_enhanced import EnhancedMatchingAgent
            agent = EnhancedMatchingAgent(self.openai_api_key)
            state = await agent.match_candidates(state)
            
            self.conversation_history.append({
                "agent": "matcher",
                "action": "match_candidates",
                "result": f"Found {len(state['matched_candidates'])} top matches"
            })
            
            # Research
            from agents.research_agent_enhanced import EnhancedResearchAgent
            agent = EnhancedResearchAgent(self.openai_api_key)
            state = await agent.research_candidates(state)
            
            self.conversation_history.append({
                "agent": "researcher",
                "action": "research_candidates",
                "result": f"Researched {len(state['researched_candidates'])} candidates"
            })
            
            # Validation
            from agents.validation_agent import ValidationAgent
            agent = ValidationAgent(self.openai_api_key)
            state = await agent.validate_candidates(state)
            
            self.conversation_history.append({
                "agent": "validator",
                "action": "validate_candidates",
                "result": f"Validated {len(state['validated_candidates'])} candidates"
            })
            
            # Summarization
            from agents.summarization_agent import SummarizationAgent
            agent = SummarizationAgent(self.openai_api_key)
            state = await agent.create_final_reports(state)
            
            self.conversation_history.append({
                "agent": "summarizer",
                "action": "create_reports",
                "result": f"Generated {len(state['candidate_reports'])} reports"
            })
            
            # Add conversation history to state
            state["agent_messages"] = self.conversation_history
            
            logger.info("✅ AutoGen-style workflow completed")
            return state
            
        except Exception as e:
            logger.error(f"❌ AutoGen workflow failed: {e}")
            initial_state["errors"].append(f"Workflow execution failed: {str(e)}")
            return initial_state


# Factory function to create the appropriate workflow
def create_workflow(workflow_type: str = "langgraph", **kwargs):
    """
    Factory function to create the appropriate workflow implementation
    
    Args:
        workflow_type: "langgraph", "crewai", or "autogen"
        **kwargs: Additional arguments for workflow initialization
    
    Returns:
        Workflow instance
    """
    
    if workflow_type.lower() == "langgraph":
        return ResumeAnalysisWorkflow(**kwargs)
    elif workflow_type.lower() == "crewai":
        return CrewAIStyleWorkflow(**kwargs)
    elif workflow_type.lower() == "autogen":
        return AutoGenStyleWorkflow(**kwargs)
    else:
        raise ValueError(f"Unknown workflow type: {workflow_type}")


# Main workflow execution function
async def run_multi_agent_workflow(
    job_requirements,
    uploaded_files: list,
    openai_api_key: str,
    workflow_type: str = "langgraph",
    **kwargs
) -> AgentState:
    """
    Main function to run the multi-agent workflow
    
    Args:
        job_requirements: Job requirements object
        uploaded_files: List of uploaded file paths
        openai_api_key: OpenAI API key
        workflow_type: Type of workflow to use ("langgraph", "crewai", "autogen")
        **kwargs: Additional arguments
    
    Returns:
        Final state with results
    """
    
    try:
        # Create initial state
        from state_management import create_initial_state
        initial_state = create_initial_state(job_requirements, uploaded_files)
        
        # Create workflow
        workflow = create_workflow(
            workflow_type=workflow_type,
            openai_api_key=openai_api_key,
            **kwargs
        )
        
        # Execute workflow
        if workflow_type.lower() == "langgraph":
            final_state = await workflow.run_workflow(initial_state)
        elif workflow_type.lower() == "crewai":
            final_state = await workflow.execute_crew_workflow(initial_state)
        elif workflow_type.lower() == "autogen":
            final_state = await workflow.execute_autogen_workflow(initial_state)
        
        return final_state
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        initial_state["errors"].append(f"Workflow execution failed: {str(e)}")
        return initial_state


# Usage example
if __name__ == "__main__":
    import os
    from state_management import JobRequirements
    
    # Example usage
    job_requirements = JobRequirements(
        position_title="Senior Software Engineer",
        job_description="We are looking for a senior software engineer...",
        required_skills=["Python", "React", "AWS"],
        company_name="Example Corp"
    )
    
    uploaded_files = ["resume1.pdf", "resume2.pdf"]
    openai_api_key = os.getenv("OPENAI_API_KEY")
    
    async def main():
        # Test LangGraph workflow
        result = await run_multi_agent_workflow(
            job_requirements=job_requirements,
            uploaded_files=uploaded_files,
            openai_api_key=openai_api_key,
            workflow_type="langgraph"
        )
        
        print(f"Workflow completed with {len(result['candidate_reports'])} reports")
    
    asyncio.run(main())