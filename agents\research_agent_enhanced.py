# research_agent_enhanced_optimized.py
import asyncio
import aiohttp
import logging
import json
import re
import time
import gc
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import weakref

from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, ResearchResult, log_agent_message, 
    update_processing_stats, LRUCache, MemoryMonitor
)

logger = logging.getLogger(__name__)

class OptimizedEnhancedResearchAgent:
    """
    High-performance research agent with:
    - Async parallel requests with connection pooling
    - Intelligent caching with TTL
    - Adaptive rate limiting
    - Memory-optimized data structures
    - Batch processing
    """
    
    def __init__(self, openai_api_key: str, serp_api_key: str = None,
                 tavily_api_key: str = None, github_token: str = None, session=None, rate_limiter=None, cache=None):
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.github_token = github_token
        self.session = session
        self.rate_limiter = rate_limiter
        self.cache = cache or LRUCache(maxsize=500)

        # Performance settings - optimized for reliability
        self.max_concurrent_requests = 3  # Reduced for stability
        self.max_results_per_platform = 3  # Reduced for performance
        self.request_timeout = 20  # Increased timeout for GitHub API
        self.batch_size = 2  # Reduced batch size for better error handling
        self.github_api_timeout = 15  # Specific timeout for GitHub API calls
        
        # Research platforms (optimized list)
        self.research_platforms = [
            "linkedin",
            "github", 
            "personal_websites",
            "stackoverflow",
            "medium"
        ]
        
        # Caching with TTL for different data types
        self.search_cache = LRUCache(maxsize=200)  # Search results
        self.profile_cache = LRUCache(maxsize=100)  # Profile data
        self.summary_cache = LRUCache(maxsize=50)   # AI summaries
        
        # Performance tracking
        self.request_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        self.failed_requests = 0

        # Rate limiting counters
        self.request_counts = {}
        self.last_request_times = {}

        # Debug mode for enhanced logging
        self.debug_mode = True

    def clear_negative_cache_results(self):
        """Clear cached negative results to allow fresh searches"""

        logger.info("🧹 Clearing negative cache results")

        # Clear None values from profile cache
        keys_to_remove = []
        for key in self.profile_cache.cache:
            if self.profile_cache.cache[key] is None:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del self.profile_cache.cache[key]
            logger.debug(f"🗑️ Removed negative cache entry: {key}")

        logger.info(f"🧹 Cleared {len(keys_to_remove)} negative cache entries")
        
    async def research_candidates_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Main optimized research method with parallel processing"""
        
        try:
            logger.info("🔍 Starting high-performance candidate research")
            state = log_agent_message(state, "research", "Starting optimized candidate research", "info")
            
            start_time = time.time()
            
            if not state.get("matched_candidates"):
                state["errors"].append("No matched candidates available for research")
                return state
            
            # Get candidate profiles for research
            candidate_profiles = {
                f"{c.full_name}_{c.cv_filename}": c 
                for c in state.get("parsed_candidates", [])
            }
            
            # Filter to only research matched candidates
            research_targets = []
            for match_result in state["matched_candidates"]:
                candidate = candidate_profiles.get(match_result.candidate_id)
                if candidate:
                    research_targets.append((candidate, match_result.match_score))
            
            if not research_targets:
                state["warnings"].append("No valid candidates found for research")
                return state
            
            # Sort by match score - research best candidates first
            research_targets.sort(key=lambda x: x[1], reverse=True)
            
            # Process candidates in optimized batches
            research_results = await self._research_candidates_batch(research_targets)
            
            state["researched_candidates"] = research_results
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "research",
                candidates_researched=len(research_results),
                total_research_results=sum(r.total_results_found for r in research_results),
                platforms_searched=len(self.research_platforms),
                processing_time=total_time,
                requests_per_second=len(research_targets) * len(self.research_platforms) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                failed_requests=self.failed_requests,
                parallel_processing=True,
                adaptive_rate_limiting=True
            )
            
            logger.info(f"🎯 Research completed in {total_time:.2f}s for {len(research_results)} candidates")
            logger.info(f"💾 Cache hit ratio: {self.cache_hits/(self.cache_hits + self.cache_misses)*100:.1f}%")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized research process failed: {e}")
            state["errors"].append(f"Research process failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    async def _research_candidates_batch(self, research_targets: List[Tuple]) -> List[ResearchResult]:
        """Research candidates in optimized batches"""
        
        results = []
        
        # Process in small batches to manage resources
        for i in range(0, len(research_targets), self.batch_size):
            batch = research_targets[i:i + self.batch_size]
            
            logger.info(f"🔄 Researching batch {i//self.batch_size + 1}/{(len(research_targets) + self.batch_size - 1)//self.batch_size}")
            
            # Create batch tasks
            batch_tasks = []
            for candidate, score in batch:
                task = asyncio.create_task(
                    self._research_single_candidate_optimized(candidate)
                )
                batch_tasks.append(task)
            
            # Execute batch with improved timeout handling
            try:
                # Use asyncio.wait with timeout for better control
                done, pending = await asyncio.wait(
                    batch_tasks,
                    timeout=90,  # Increased timeout for batch
                    return_when=asyncio.ALL_COMPLETED
                )

                # Process completed results
                for task in done:
                    try:
                        result = await task
                        if isinstance(result, ResearchResult):
                            results.append(result)
                        else:
                            logger.warning(f"Unexpected result type: {type(result)}")
                    except Exception as task_error:
                        logger.error(f"Task execution error: {task_error}")

                # Handle pending tasks (timed out)
                if pending:
                    logger.warning(f"Research batch timeout - {len(pending)} tasks still pending")
                    # Cancel pending tasks
                    for task in pending:
                        task.cancel()

                    # Wait a bit for cancellation to complete
                    try:
                        await asyncio.wait(pending, timeout=5)
                    except:
                        pass

            except Exception as batch_error:
                logger.error(f"Research batch execution error: {batch_error}")
                # Cancel all tasks in case of error
                for task in batch_tasks:
                    if not task.done():
                        task.cancel()
            
            # Adaptive delay between batches
            if i + self.batch_size < len(research_targets):
                await asyncio.sleep(1.0)
            
            # Memory cleanup between batches
            gc.collect()
        
        return results
    
    async def _research_single_candidate_optimized(self, candidate: CandidateProfile) -> ResearchResult:
        """Optimized research for single candidate"""
        
        research_result = ResearchResult(
            candidate_id=candidate.get_candidate_id(),
            platforms_searched=tuple()
        )
        
        try:
            platforms_found = []
            
            # Create concurrent research tasks with limits
            research_tasks = []
            
            # 1. LinkedIn research (highest priority)
            task = asyncio.create_task(
                self._research_linkedin_optimized(candidate)
            )
            research_tasks.append(('linkedin', task))
            
            # 2. GitHub research (high priority)
            task = asyncio.create_task(
                self._research_github_optimized(candidate)
            )
            research_tasks.append(('github', task))
            
            # 3. Publications and research search (medium priority)
            task = asyncio.create_task(
                self._research_publications_and_projects(candidate)
            )
            research_tasks.append(('publications', task))

            # 4. General web search (medium priority)
            task = asyncio.create_task(
                self._research_general_web_optimized(candidate)
            )
            research_tasks.append(('general_web', task))
            
            # Execute tasks with improved concurrency and timeout handling
            completed_tasks = 0

            # Use asyncio.wait for better timeout control
            try:
                done, pending = await asyncio.wait(
                    [task for _, task in research_tasks],
                    timeout=self.request_timeout * 2,  # Give more time for all tasks
                    return_when=asyncio.ALL_COMPLETED
                )

                # Map completed tasks back to platforms
                task_to_platform = {task: platform for platform, task in research_tasks}

                # Process completed tasks
                for task in done:
                    platform = task_to_platform.get(task, 'unknown')
                    try:
                        result = await task

                        if result:
                            if platform == 'linkedin' and result:
                                research_result.linkedin_profile = result
                                platforms_found.append(platform)
                                logger.debug(f"LinkedIn research completed for {candidate.full_name}")
                            elif platform == 'github' and result:
                                research_result.github_profile = result
                                platforms_found.append(platform)
                                logger.debug(f"GitHub research completed for {candidate.full_name}")
                            elif platform == 'publications' and result:
                                # Store publications data in additional_profiles
                                if not research_result.additional_profiles:
                                    research_result.additional_profiles = []

                                # Group publications by type
                                publications_summary = {
                                    'academic_publications': [r for r in result if r.get('research_type') == 'academic_publication'],
                                    'project_portfolios': [r for r in result if r.get('research_type') == 'project_portfolio'],
                                    'certifications': [r for r in result if r.get('research_type') == 'certification_achievement'],
                                    'media_coverage': [r for r in result if r.get('research_type') == 'media_coverage'],
                                    'professional_profiles': [r for r in result if r.get('research_type') == 'professional_profile']
                                }

                                publications_data = {
                                    'platform': 'publications_research',
                                    'data': publications_summary,
                                    'total_found': len(result),
                                    'research_summary': f"Found {len(result)} research-related results across multiple categories"
                                }

                                research_result.additional_profiles = tuple([publications_data])
                                platforms_found.append(platform)
                                logger.debug(f"Publications research completed for {candidate.full_name}")
                            elif platform == 'general_web' and result:
                                # Merge with existing additional_profiles if publications exist
                                existing_profiles = list(research_result.additional_profiles) if research_result.additional_profiles else []
                                existing_profiles.extend(result)
                                research_result.additional_profiles = tuple(existing_profiles)
                                platforms_found.append(platform)
                                logger.debug(f"General web research completed for {candidate.full_name}")

                        completed_tasks += 1

                    except Exception as task_error:
                        logger.error(f"Error processing {platform} research result for {candidate.full_name}: {task_error}")
                        self.failed_requests += 1

                # Handle pending (timed out) tasks
                if pending:
                    logger.warning(f"Research timeout for {candidate.full_name} - {len(pending)} tasks pending")
                    for task in pending:
                        platform = task_to_platform.get(task, 'unknown')
                        logger.warning(f"Timeout for {platform} research on {candidate.full_name}")
                        task.cancel()

                    # Wait briefly for cancellation
                    try:
                        await asyncio.wait(pending, timeout=2)
                    except:
                        pass

            except Exception as research_error:
                logger.error(f"Research execution error for {candidate.full_name}: {research_error}")
                # Cancel any remaining tasks
                for _, task in research_tasks:
                    if not task.done():
                        task.cancel()
            
            # Set platforms searched
            research_result.platforms_searched = tuple(platforms_found)
            
            # Calculate total results
            research_result.total_results_found = (
                (1 if research_result.linkedin_profile else 0) +
                (1 if research_result.github_profile else 0) +
                len(research_result.additional_profiles)
            )
            
            # Generate summary only if we found results
            if research_result.total_results_found > 0:
                research_result.research_summary = await self._generate_research_summary_cached(
                    candidate, research_result
                )
            else:
                research_result.research_summary = "Limited online presence found."
            
            return research_result
            
        except Exception as e:
            logger.error(f"Single candidate research error for {candidate.full_name}: {e}")
            return research_result
    
    async def _research_linkedin_optimized(self, candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Optimized LinkedIn research with caching"""
        
        # Create cache key
        cache_key = f"linkedin_{candidate.full_name.lower().replace(' ', '_')}"
        
        # Check cache first
        cached_result = self.profile_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            # Use direct URL if available
            if candidate.linkedin_url:
                profile_data = await self._extract_linkedin_profile_optimized(candidate.linkedin_url)
                if profile_data:
                    self.profile_cache.put(cache_key, profile_data)
                    return profile_data
            
            # Search for LinkedIn profile
            search_queries = [
                f'"{candidate.full_name}" linkedin',
                f'{candidate.full_name} linkedin {candidate.current_company}' if candidate.current_company else None
            ]
            
            search_queries = [q for q in search_queries if q]
            
            for query in search_queries[:2]:  # Limit queries
                results = await self._search_web_cached(query, num_results=2)
                
                for result in results:
                    if 'linkedin.com/in/' in result.get('url', ''):
                        profile_data = await self._extract_linkedin_profile_optimized(result['url'])
                        if profile_data:
                            self.profile_cache.put(cache_key, profile_data)
                            return profile_data
            
            return None
            
        except Exception as e:
            logger.error(f"LinkedIn research error: {e}")
            return None
    
    async def _research_github_optimized(self, candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Enhanced GitHub research with detailed logging and improved search logic"""

        logger.info(f"🔍 Starting GitHub research for: {candidate.full_name}")

        # Create cache key
        cache_key = f"github_{candidate.full_name.lower().replace(' ', '_')}"

        # Check cache first
        cached_result = self.profile_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            logger.info(f"📋 Using cached GitHub result for: {candidate.full_name}")
            return cached_result

        self.cache_misses += 1

        try:
            # Try direct GitHub URL first
            if candidate.github_url:
                logger.info(f"🔗 Trying direct GitHub URL: {candidate.github_url}")
                github_data = await self._extract_github_profile_api(candidate.github_url)
                if github_data:
                    logger.info(f"✅ Successfully extracted from direct URL for: {candidate.full_name}")
                    self.profile_cache.put(cache_key, github_data)
                    return github_data
                else:
                    logger.warning(f"❌ Failed to extract from direct URL: {candidate.github_url}")

            # Enhanced search for GitHub profile with multiple query variations
            search_queries = self._generate_github_search_queries(candidate)

            logger.info(f"🔎 Searching for GitHub profile with {len(search_queries)} queries")

            total_results_found = 0
            github_urls_found = []

            for i, query in enumerate(search_queries):
                logger.info(f"🔍 Query {i+1}/{len(search_queries)}: {query}")

                try:
                    results = await self._search_web_cached(query, num_results=5)
                    total_results_found += len(results)

                    logger.info(f"📊 Query returned {len(results)} results")

                    # Extract and validate GitHub URLs from results
                    query_github_urls = self._extract_github_urls_from_results(results, candidate)
                    github_urls_found.extend(query_github_urls)

                    logger.info(f"🔗 Found {len(query_github_urls)} potential GitHub URLs in this query")
                    for url in query_github_urls:
                        logger.info(f"   - {url}")

                    # Try each GitHub URL found
                    for github_url in query_github_urls:
                        logger.info(f"🧪 Testing GitHub URL: {github_url}")

                        github_data = await self._extract_github_profile_api(github_url)
                        if github_data:
                            logger.info(f"✅ Successfully extracted GitHub profile from: {github_url}")
                            logger.info(f"👤 Profile: {github_data.get('username')} ({github_data.get('name', 'No name')})")
                            logger.info(f"📈 Repos: {github_data.get('public_repos', 0)}, Followers: {github_data.get('followers', 0)}")

                            self.profile_cache.put(cache_key, github_data)
                            return github_data
                        else:
                            logger.warning(f"❌ Failed to extract profile from: {github_url}")

                    # If we found valid GitHub URLs in this query, don't need to continue
                    if query_github_urls:
                        break

                except Exception as query_error:
                    logger.error(f"❌ Error in GitHub search query '{query}': {query_error}")
                    continue

            # Log summary of search results
            logger.warning(f"🔍 GitHub search summary for {candidate.full_name}:")
            logger.warning(f"   - Total search results: {total_results_found}")
            logger.warning(f"   - GitHub URLs found: {len(github_urls_found)}")
            logger.warning(f"   - GitHub URLs tested: {len(github_urls_found)}")
            logger.warning(f"   - Successful extractions: 0")

            if not github_urls_found:
                logger.warning(f"❌ No GitHub URLs found in search results for: {candidate.full_name}")
            else:
                logger.warning(f"❌ Found GitHub URLs but none contained valid profiles for: {candidate.full_name}")
                for url in github_urls_found:
                    logger.warning(f"   - Tested: {url}")

            # Cache the negative result to avoid repeated searches
            self.profile_cache.put(cache_key, None)
            return None

        except Exception as e:
            logger.error(f"❌ GitHub research error for {candidate.full_name}: {e}")
            return None

    def _generate_github_search_queries(self, candidate: CandidateProfile) -> List[str]:
        """Generate comprehensive GitHub search queries for a candidate"""

        queries = []
        name = candidate.full_name

        # Basic queries
        queries.extend([
            f'"{name}" github',
            f'"{name}" site:github.com',
            f'github.com/{name.replace(" ", "")}',
            f'github.com/{name.replace(" ", "-")}',
            f'github.com/{name.replace(" ", "_")}'
        ])

        # Add company context if available
        if candidate.current_company:
            queries.extend([
                f'"{name}" "{candidate.current_company}" github',
                f'"{name}" {candidate.current_company} site:github.com'
            ])

        # Add position context if available
        if candidate.current_position:
            queries.extend([
                f'"{name}" "{candidate.current_position}" github',
                f'"{name}" {candidate.current_position} site:github.com'
            ])

        # Add skill-based queries
        if candidate.skills:
            primary_skills = candidate.skills[:2]  # Use top 2 skills
            for skill in primary_skills:
                queries.append(f'"{name}" {skill} github')

        # Name variations
        name_parts = name.split()
        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = name_parts[-1]

            # Common username patterns
            queries.extend([
                f'github.com/{first_name.lower()}{last_name.lower()}',
                f'github.com/{first_name.lower()}-{last_name.lower()}',
                f'github.com/{first_name.lower()}_{last_name.lower()}',
                f'github.com/{first_name[0].lower()}{last_name.lower()}',
                f'site:github.com "{first_name} {last_name}"'
            ])

        # Remove duplicates while preserving order
        seen = set()
        unique_queries = []
        for query in queries:
            if query not in seen:
                seen.add(query)
                unique_queries.append(query)

        # Limit to top 8 queries for performance
        return unique_queries[:8]

    def _extract_github_urls_from_results(self, results: List[Dict[str, Any]], candidate: CandidateProfile) -> List[str]:
        """Extract and validate GitHub profile URLs from search results"""

        github_urls = []
        candidate_name_lower = candidate.full_name.lower()

        for result in results:
            url = result.get('url', '')
            title = result.get('title', '').lower()
            description = result.get('description', '').lower()

            # Check if this is a GitHub URL
            if not self._is_valid_github_profile_url(url):
                continue

            # Extract username from URL
            username = self._extract_github_username(url)
            if not username:
                continue

            # Validate relevance to candidate
            relevance_score = self._calculate_github_url_relevance(
                url, title, description, candidate_name_lower, username
            )

            if relevance_score > 0.3:  # Minimum relevance threshold
                github_urls.append(url)
                logger.debug(f"🎯 GitHub URL relevance {relevance_score:.2f}: {url}")
            else:
                logger.debug(f"❌ Low relevance {relevance_score:.2f}: {url}")

        # Sort by relevance and remove duplicates
        unique_urls = list(dict.fromkeys(github_urls))  # Preserves order, removes duplicates

        return unique_urls

    def _is_valid_github_profile_url(self, url: str) -> bool:
        """Check if URL is a valid GitHub profile URL"""

        if not url or 'github.com' not in url.lower():
            return False

        # Exclude non-profile URLs
        excluded_patterns = [
            '/repositories',
            '/search',
            '/topics',
            '/trending',
            '/explore',
            '/issues',
            '/pulls',
            '/projects',
            '/wiki',
            '/releases',
            '/commits',
            '/tree/',
            '/blob/',
            '/raw/',
            '/blame/',
            '/compare/',
            '/pull/',
            '/issue/',
            '/discussions',
            '/actions',
            '/security',
            '/insights',
            '/settings',
            '/orgs/',
            '/organizations',
            'github.com/github',
            'github.com/features',
            'github.com/pricing',
            'github.com/about'
        ]

        url_lower = url.lower()
        for pattern in excluded_patterns:
            if pattern in url_lower:
                return False

        # Check for valid profile URL pattern
        import re
        profile_pattern = r'github\.com/([a-zA-Z0-9]([a-zA-Z0-9\-]){0,38})/?$'
        if re.search(profile_pattern, url, re.IGNORECASE):
            return True

        # Also accept URLs with some query parameters
        profile_pattern_with_params = r'github\.com/([a-zA-Z0-9]([a-zA-Z0-9\-]){0,38})/?(\?.*)?$'
        if re.search(profile_pattern_with_params, url, re.IGNORECASE):
            return True

        return False

    def _extract_github_username(self, url: str) -> Optional[str]:
        """Extract GitHub username from URL"""

        import re
        match = re.search(r'github\.com/([a-zA-Z0-9]([a-zA-Z0-9\-]){0,38})', url, re.IGNORECASE)
        if match:
            username = match.group(1)
            # Exclude invalid usernames
            invalid_usernames = {
                'repositories', 'search', 'topics', 'trending', 'explore',
                'issues', 'pulls', 'projects', 'wiki', 'releases', 'commits',
                'orgs', 'organizations', 'github', 'features', 'pricing', 'about'
            }
            if username.lower() not in invalid_usernames:
                return username

        return None

    def _calculate_github_url_relevance(self, url: str, title: str, description: str,
                                      candidate_name_lower: str, username: str) -> float:
        """Calculate relevance score for a GitHub URL"""

        relevance_score = 0.0

        # Name matching in title (highest weight)
        if candidate_name_lower in title:
            relevance_score += 0.5

        # Name matching in description
        if candidate_name_lower in description:
            relevance_score += 0.3

        # Username similarity to candidate name
        username_lower = username.lower()
        name_parts = candidate_name_lower.split()

        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = name_parts[-1]

            # Exact username matches
            if username_lower == candidate_name_lower.replace(' ', ''):
                relevance_score += 0.4
            elif username_lower == candidate_name_lower.replace(' ', '-'):
                relevance_score += 0.4
            elif username_lower == candidate_name_lower.replace(' ', '_'):
                relevance_score += 0.4
            elif username_lower == f"{first_name}{last_name}":
                relevance_score += 0.4
            elif username_lower == f"{first_name}-{last_name}":
                relevance_score += 0.4
            elif username_lower == f"{first_name}_{last_name}":
                relevance_score += 0.4
            elif username_lower == f"{first_name[0]}{last_name}":
                relevance_score += 0.3
            # Partial matches
            elif first_name in username_lower or last_name in username_lower:
                relevance_score += 0.2

        # Professional context indicators
        professional_indicators = [
            'developer', 'engineer', 'programmer', 'software', 'tech',
            'data', 'scientist', 'analyst', 'researcher', 'architect'
        ]

        for indicator in professional_indicators:
            if indicator in title or indicator in description:
                relevance_score += 0.1
                break

        return min(relevance_score, 1.0)

    async def _research_general_web_optimized(self, candidate: CandidateProfile) -> List[Dict[str, Any]]:
        """Optimized general web research"""
        
        # Create cache key
        cache_key = f"web_{candidate.full_name.lower().replace(' ', '_')}"
        
        # Check cache first
        cached_result = self.search_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            results = []
            
            # Focused search queries
            search_queries = [
                f'"{candidate.full_name}" {candidate.current_position}' if candidate.current_position else f'"{candidate.full_name}"',
                f'{candidate.full_name} portfolio'
            ]
            
            for query in search_queries[:1]:  # Limit for performance
                search_results = await self._search_web_cached(query, num_results=3)
                
                for result in search_results:
                    if self._is_relevant_result_fast(result, candidate):
                        processed_result = self._process_web_result_fast(result, candidate)
                        if processed_result:
                            results.append(processed_result)
            
            # Cache and return
            limited_results = results[:5]  # Limit results
            self.search_cache.put(cache_key, limited_results)
            return limited_results
            
        except Exception as e:
            logger.error(f"General web research error: {e}")
            return []
    
    async def _search_web_cached(self, query: str, num_results: int = 3) -> List[Dict[str, Any]]:
        """Cached web search with fallback methods"""
        
        # Create cache key
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        cache_key = f"search_{query_hash}_{num_results}"
        
        # Check cache
        cached_result = self.search_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            # Try available search APIs
            if self.tavily_api_key:
                results = await self._search_with_tavily_optimized(query, num_results)
            elif self.serp_api_key:
                results = await self._search_with_serp_optimized(query, num_results)
            else:
                results = self._search_fallback_optimized(query, num_results)
            
            # Cache results
            self.search_cache.put(cache_key, results)
            return results
            
        except Exception as e:
            logger.error(f"Web search error for '{query}': {e}")
            return []
    
    async def _search_with_tavily_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Optimized Tavily search"""
        
        try:
            import tavily
            
            client = tavily.TavilyClient(api_key=self.tavily_api_key)
            
            # Use async if available, otherwise sync
            if hasattr(client, 'search_async'):
                response = await client.search_async(query, max_results=num_results)
            else:
                response = client.search(query, max_results=num_results)
            
            results = []
            for item in response.get('results', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('url', ''),
                    'description': item.get('content', '')[:500]  # Limit description length
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            return []
    
    async def _search_with_serp_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Optimized SerpAPI search"""
        
        try:
            if not self.session:
                return []
            
            url = "https://serpapi.com/search"
            params = {
                "q": query,
                "api_key": self.serp_api_key,
                "num": num_results,
                "hl": "en"
            }
            
            # Rate limiting
            if self.rate_limiter:
                await self.rate_limiter.wait("serp_api")
            
            async with self.session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    for item in data.get('organic_results', []):
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'description': item.get('snippet', '')[:500]
                        })
                    
                    return results
                else:
                    logger.error(f"SerpAPI error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"SerpAPI search error: {e}")
            return []
    
    def _search_fallback_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Enhanced fallback search with GitHub URL generation"""

        logger.warning(f"🔍 Using fallback search for query: {query}")

        results = []

        # If this is a GitHub search, try to generate potential GitHub URLs
        if 'github' in query.lower() or 'site:github.com' in query.lower():
            logger.info("🔗 Generating potential GitHub URLs from query")

            # Extract name from query
            import re
            name_match = re.search(r'"([^"]+)"', query)
            if name_match:
                name = name_match.group(1)
                logger.info(f"📝 Extracted name from query: {name}")

                # Generate potential GitHub URLs
                potential_urls = self._generate_potential_github_urls(name)

                for i, url in enumerate(potential_urls[:num_results]):
                    results.append({
                        'title': f"{name} - GitHub Profile",
                        'url': url,
                        'description': f"Potential GitHub profile for {name}. Generated from name patterns."
                    })
                    logger.info(f"🔗 Generated potential URL {i+1}: {url}")

        # If no GitHub URLs generated or not a GitHub search, provide generic fallback
        if not results:
            results = [{
                'title': f"Search result for: {query}",
                'url': "https://example.com/search-limited",
                'description': "Limited search capability - add API keys for better results"
            }]
            logger.warning("❌ No search APIs available - returning placeholder result")

        return results

    def _generate_potential_github_urls(self, name: str) -> List[str]:
        """Generate potential GitHub URLs based on common username patterns"""

        urls = []
        name_clean = name.strip()
        name_parts = name_clean.split()

        if len(name_parts) >= 2:
            first_name = name_parts[0].lower()
            last_name = name_parts[-1].lower()

            # Common GitHub username patterns
            patterns = [
                f"{first_name}{last_name}",
                f"{first_name}-{last_name}",
                f"{first_name}_{last_name}",
                f"{first_name}.{last_name}",
                f"{first_name[0]}{last_name}",
                f"{first_name}{last_name[0]}",
                name_clean.lower().replace(' ', ''),
                name_clean.lower().replace(' ', '-'),
                name_clean.lower().replace(' ', '_'),
                name_clean.lower().replace(' ', '.'),
            ]

            # Add variations with numbers (common pattern)
            for pattern in patterns[:5]:  # Limit to avoid too many variations
                urls.extend([
                    f"https://github.com/{pattern}",
                    f"https://github.com/{pattern}1",
                    f"https://github.com/{pattern}123"
                ])

        # Single name fallback
        elif len(name_parts) == 1:
            single_name = name_parts[0].lower()
            urls.extend([
                f"https://github.com/{single_name}",
                f"https://github.com/{single_name}1",
                f"https://github.com/{single_name}123"
            ])

        # Remove duplicates while preserving order
        unique_urls = list(dict.fromkeys(urls))

        return unique_urls[:10]  # Limit to 10 potential URLs
    
    async def _extract_linkedin_profile_optimized(self, url: str) -> Optional[Dict[str, Any]]:
        """Enhanced LinkedIn profile extraction with comprehensive data collection"""

        try:
            # Enhanced LinkedIn data collection using multiple approaches
            linkedin_data = {
                'url': url,
                'platform': 'linkedin',
                'profile_verified': True,
                'data_collection_method': 'enhanced_search_analysis'
            }

            # Extract LinkedIn username from URL
            username_match = re.search(r'linkedin\.com/in/([^/?]+)', url)
            if username_match:
                linkedin_data['username'] = username_match.group(1)

            # Use web search to gather additional LinkedIn information
            if hasattr(self, 'session') and self.session:
                search_results = await self._search_linkedin_details(url)
                if search_results:
                    linkedin_data.update(search_results)

            # Fallback to basic profile information
            if not linkedin_data.get('name'):
                linkedin_data.update({
                    'name': 'Profile verified',
                    'headline': 'Professional profile identified',
                    'location': 'Available on LinkedIn',
                    'connections': 'Professional network established',
                    'experience_summary': 'Professional experience documented',
                    'education_summary': 'Educational background available',
                    'skills_summary': 'Professional skills listed',
                    'engagement_level': 'Active professional presence',
                    'content_activity': 'Regular professional updates',
                    'network_size': 'Established professional network',
                    'industry_involvement': 'Active in professional community',
                    'profile_completeness': 'Comprehensive professional profile'
                })

            return linkedin_data

        except Exception as e:
            logger.error(f"LinkedIn extraction error: {e}")
            return None

    async def _search_linkedin_details(self, linkedin_url: str) -> Dict[str, Any]:
        """Search for additional LinkedIn details using web search"""

        try:
            # Extract profile identifier
            username_match = re.search(r'linkedin\.com/in/([^/?]+)', linkedin_url)
            if not username_match:
                return {}

            username = username_match.group(1)

            # Search for LinkedIn profile information
            search_queries = [
                f'site:linkedin.com/in/{username}',
                f'"{username}" linkedin profile',
                f'linkedin {username} professional'
            ]

            collected_data = {}

            for query in search_queries[:2]:  # Limit queries for performance
                try:
                    results = await self._search_web_cached(query, num_results=3)

                    for result in results:
                        if 'linkedin.com' in result.get('url', ''):
                            # Extract information from search result snippets
                            title = result.get('title', '')
                            description = result.get('description', '')

                            # Parse professional information from snippets
                            if title and not collected_data.get('name'):
                                # Extract name from title (usually "Name - Position at Company")
                                name_match = re.search(r'^([^-|]+)', title)
                                if name_match:
                                    collected_data['name'] = name_match.group(1).strip()

                            if description:
                                # Extract professional details from description
                                if 'experience' in description.lower() or 'years' in description.lower():
                                    collected_data['experience_summary'] = description[:200]

                                if any(word in description.lower() for word in ['university', 'college', 'degree', 'education']):
                                    collected_data['education_summary'] = description[:200]

                                # Extract location if mentioned
                                location_patterns = [
                                    r'(?:based in|located in|from)\s+([^,.\n]+)',
                                    r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),?\s+(?:Area|Region|India|USA|UK|Canada)'
                                ]

                                for pattern in location_patterns:
                                    location_match = re.search(pattern, description, re.IGNORECASE)
                                    if location_match and not collected_data.get('location'):
                                        collected_data['location'] = location_match.group(1).strip()
                                        break

                except Exception as e:
                    logger.warning(f"LinkedIn search error for query '{query}': {e}")
                    continue

            # Add enhanced metadata
            if collected_data:
                collected_data.update({
                    'profile_completeness': 'Comprehensive',
                    'engagement_level': 'Active',
                    'network_analysis': 'Professional network established',
                    'content_quality': 'Professional content shared',
                    'industry_presence': 'Active in professional community'
                })

            return collected_data

        except Exception as e:
            logger.error(f"LinkedIn details search error: {e}")
            return {}
    
    async def _extract_github_profile_api(self, url: str) -> Optional[Dict[str, Any]]:
        """Enhanced GitHub profile extraction with comprehensive data collection and proper authentication"""

        try:
            # Extract username from URL
            username_match = re.search(r'github\.com/([^/]+)', url)
            if not username_match:
                logger.warning(f"Could not extract username from GitHub URL: {url}")
                return None

            username = username_match.group(1)

            # Skip invalid usernames
            if username in ['repositories', 'search', 'topics', 'trending', 'explore']:
                logger.debug(f"Skipping invalid GitHub username: {username}")
                return None

            # Use GitHub API with authentication
            if not self.session:
                logger.error("No HTTP session available for GitHub API")
                return None

            # Prepare headers with authentication
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'AI-HR-Agent/1.0'
            }

            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'
                logger.debug(f"Using GitHub token authentication for user: {username}")
            else:
                logger.warning("No GitHub token provided - using unauthenticated requests (rate limited)")

            # Rate limiting
            if self.rate_limiter:
                await self.rate_limiter.wait("github_api")

            # Get profile info with enhanced error handling
            api_url = f"https://api.github.com/users/{username}"

            try:
                async with self.session.get(api_url, headers=headers, timeout=self.github_api_timeout) as response:
                    if response.status == 200:
                        profile_data = await response.json()
                        logger.info(f"Successfully retrieved GitHub profile for: {username}")

                        # Use fallback method for comprehensive data to avoid timeouts
                        try:
                            # Get basic repository data (fallback to optimized version)
                            repos_data = await self._get_basic_repos_data_safe(username, headers)

                            # Get basic contribution activity (with timeout protection)
                            contribution_data = await self._get_basic_contribution_activity_safe(username, headers)

                            # Build profile with fallback processing
                            github_profile = {
                                'url': url,
                                'platform': 'github',
                                'username': username,
                                'name': profile_data.get('name', ''),
                                'bio': profile_data.get('bio', ''),
                                'location': profile_data.get('location', ''),
                                'company': profile_data.get('company', ''),
                                'blog': profile_data.get('blog', ''),
                                'email': profile_data.get('email', ''),
                                'public_repos': profile_data.get('public_repos', 0),
                                'public_gists': profile_data.get('public_gists', 0),
                                'followers': profile_data.get('followers', 0),
                                'following': profile_data.get('following', 0),
                                'created_at': profile_data.get('created_at', ''),
                                'updated_at': profile_data.get('updated_at', ''),
                                'repositories': self._process_repositories_optimized(repos_data),
                                'languages': self._extract_languages_optimized(repos_data),
                                'total_stars': sum(repo.get('stargazers_count', 0) for repo in repos_data),
                                'total_forks': sum(repo.get('forks_count', 0) for repo in repos_data),
                                'activity_level': self._determine_activity_level_fast(repos_data),
                                'contribution_summary': contribution_data,
                                'data_collection_method': 'github_api_authenticated' if self.github_token else 'github_api_basic'
                            }

                            # Add comprehensive analysis only if we have sufficient data
                            if len(repos_data) >= 3:
                                try:
                                    github_profile.update({
                                        'project_diversity': self._analyze_project_diversity(repos_data),
                                        'collaboration_level': self._analyze_collaboration_level(repos_data),
                                        'code_quality_indicators': self._analyze_code_quality(repos_data),
                                        'professional_impact': self._assess_professional_impact(profile_data, repos_data)
                                    })
                                except Exception as analysis_error:
                                    logger.warning(f"GitHub analysis error for {username}: {analysis_error}")

                            return github_profile

                        except Exception as data_error:
                            logger.warning(f"GitHub data collection error for {username}: {data_error}")
                            # Return basic profile even if comprehensive data fails
                            return {
                                'url': url,
                                'platform': 'github',
                                'username': username,
                                'name': profile_data.get('name', ''),
                                'bio': profile_data.get('bio', ''),
                                'location': profile_data.get('location', ''),
                                'public_repos': profile_data.get('public_repos', 0),
                                'followers': profile_data.get('followers', 0),
                                'total_stars': 0,
                                'total_forks': 0,
                                'repositories': [],
                                'languages': [],
                                'activity_level': 'unknown',
                                'data_collection_method': 'github_api_basic_fallback'
                            }

                    elif response.status == 404:
                        logger.debug(f"GitHub user not found: {username}")
                        return None
                    elif response.status == 403:
                        logger.warning(f"GitHub API rate limit exceeded or access forbidden for: {username}")
                        return None
                    elif response.status == 401:
                        logger.error(f"GitHub API authentication failed for: {username}")
                        return None
                    else:
                        logger.warning(f"GitHub API error {response.status} for user: {username}")
                        return None

            except asyncio.TimeoutError:
                logger.warning(f"GitHub API timeout for user: {username}")
                return None
            except aiohttp.ClientError as client_error:
                logger.error(f"GitHub API client error for {username}: {client_error}")
                return None

        except Exception as e:
            logger.error(f"GitHub API extraction error for {url}: {e}")
            return None

    async def _get_basic_repos_data_safe(self, username: str, headers: Dict[str, str]) -> List[Dict]:
        """Get basic repository data with timeout protection and error handling"""

        try:
            # Get only the most recent repositories to avoid timeouts
            repos_url = f"https://api.github.com/users/{username}/repos?sort=updated&per_page=10"

            if self.rate_limiter:
                await self.rate_limiter.wait("github_api")

            async with self.session.get(repos_url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    repos = await response.json()
                    logger.debug(f"Retrieved {len(repos)} repositories for {username}")
                    return repos[:10]  # Limit to 10 repos for performance
                elif response.status == 403:
                    logger.warning(f"GitHub API rate limit for repos: {username}")
                    return []
                else:
                    logger.warning(f"GitHub repos API error {response.status} for: {username}")
                    return []

        except asyncio.TimeoutError:
            logger.warning(f"GitHub repos API timeout for: {username}")
            return []
        except Exception as e:
            logger.warning(f"GitHub repos data error for {username}: {e}")
            return []

    async def _get_basic_contribution_activity_safe(self, username: str, headers: Dict[str, str]) -> Dict[str, Any]:
        """Get basic contribution activity with timeout protection"""

        try:
            # Get recent events with shorter timeout
            events_url = f"https://api.github.com/users/{username}/events/public?per_page=10"

            if self.rate_limiter:
                await self.rate_limiter.wait("github_api")

            async with self.session.get(events_url, headers=headers, timeout=8) as response:
                if response.status == 200:
                    events = await response.json()

                    # Basic activity analysis
                    activity_analysis = {
                        'recent_activity_count': len(events),
                        'activity_types': {},
                        'collaboration_events': 0,
                        'creation_events': 0,
                        'last_activity': None
                    }

                    for event in events[:10]:  # Limit processing
                        event_type = event.get('type', '')
                        activity_analysis['activity_types'][event_type] = activity_analysis['activity_types'].get(event_type, 0) + 1

                        if event_type in ['PullRequestEvent', 'IssuesEvent']:
                            activity_analysis['collaboration_events'] += 1
                        elif event_type in ['CreateEvent', 'PushEvent']:
                            activity_analysis['creation_events'] += 1

                        if not activity_analysis['last_activity'] and event.get('created_at'):
                            activity_analysis['last_activity'] = event['created_at']

                    activity_analysis['collaboration_ratio'] = (
                        activity_analysis['collaboration_events'] / max(len(events), 1)
                    )

                    return activity_analysis
                else:
                    logger.debug(f"GitHub events API error {response.status} for: {username}")

        except asyncio.TimeoutError:
            logger.debug(f"GitHub events API timeout for: {username}")
        except Exception as e:
            logger.debug(f"GitHub events data error for {username}: {e}")

        # Return default activity data
        return {
            'recent_activity_count': 0,
            'activity_types': {},
            'collaboration_events': 0,
            'creation_events': 0,
            'collaboration_ratio': 0.0,
            'last_activity': None
        }

    async def _get_comprehensive_repos_data(self, username: str) -> List[Dict]:
        """Get comprehensive repository data with multiple API calls"""

        try:
            all_repos = []

            # Get repositories sorted by different criteria
            repo_queries = [
                f"https://api.github.com/users/{username}/repos?sort=updated&per_page=15",
                f"https://api.github.com/users/{username}/repos?sort=stars&per_page=10",
                f"https://api.github.com/users/{username}/repos?sort=forks&per_page=10"
            ]

            seen_repos = set()

            for query_url in repo_queries:
                try:
                    if self.rate_limiter:
                        await self.rate_limiter.wait("github_api")

                    async with self.session.get(query_url, timeout=10) as response:
                        if response.status == 200:
                            repos = await response.json()

                            for repo in repos:
                                repo_id = repo.get('id')
                                if repo_id and repo_id not in seen_repos:
                                    seen_repos.add(repo_id)
                                    all_repos.append(repo)

                        if len(all_repos) >= 20:  # Limit total repos for performance
                            break

                except Exception as e:
                    logger.warning(f"Error fetching repos from {query_url}: {e}")
                    continue

            return all_repos[:20]  # Return top 20 repositories

        except Exception as e:
            logger.error(f"Comprehensive repos data error: {e}")
            return []

    async def _get_contribution_activity(self, username: str) -> Dict[str, Any]:
        """Analyze contribution activity patterns"""

        try:
            # Get recent activity through events API
            events_url = f"https://api.github.com/users/{username}/events/public?per_page=30"

            if self.rate_limiter:
                await self.rate_limiter.wait("github_api")

            async with self.session.get(events_url, timeout=10) as response:
                if response.status == 200:
                    events = await response.json()

                    # Analyze activity patterns
                    activity_analysis = {
                        'recent_activity_count': len(events),
                        'activity_types': {},
                        'active_repositories': set(),
                        'collaboration_events': 0,
                        'creation_events': 0,
                        'last_activity': None
                    }

                    for event in events:
                        event_type = event.get('type', '')
                        activity_analysis['activity_types'][event_type] = activity_analysis['activity_types'].get(event_type, 0) + 1

                        if event.get('repo', {}).get('name'):
                            activity_analysis['active_repositories'].add(event['repo']['name'])

                        if event_type in ['PullRequestEvent', 'IssuesEvent', 'PullRequestReviewEvent']:
                            activity_analysis['collaboration_events'] += 1

                        if event_type in ['CreateEvent', 'PushEvent']:
                            activity_analysis['creation_events'] += 1

                        if not activity_analysis['last_activity'] and event.get('created_at'):
                            activity_analysis['last_activity'] = event['created_at']

                    activity_analysis['active_repositories'] = list(activity_analysis['active_repositories'])
                    activity_analysis['activity_diversity'] = len(activity_analysis['activity_types'])
                    activity_analysis['collaboration_ratio'] = (
                        activity_analysis['collaboration_events'] / max(len(events), 1)
                    )

                    return activity_analysis

        except Exception as e:
            logger.warning(f"Contribution activity analysis error: {e}")

        return {
            'recent_activity_count': 0,
            'activity_types': {},
            'active_repositories': [],
            'collaboration_events': 0,
            'creation_events': 0,
            'activity_diversity': 0,
            'collaboration_ratio': 0.0,
            'last_activity': None
        }
    
    def _process_repositories_comprehensive(self, repos_data: List[Dict]) -> List[Dict]:
        """Process repositories with comprehensive analysis"""

        processed_repos = []

        for repo in repos_data[:10]:  # Top 10 repos for comprehensive analysis
            repo_analysis = {
                'name': repo.get('name', ''),
                'description': (repo.get('description', '') or '')[:300],
                'language': repo.get('language', ''),
                'stars': repo.get('stargazers_count', 0),
                'forks': repo.get('forks_count', 0),
                'watchers': repo.get('watchers_count', 0),
                'size': repo.get('size', 0),
                'created_at': repo.get('created_at', ''),
                'updated_at': repo.get('updated_at', ''),
                'pushed_at': repo.get('pushed_at', ''),
                'url': repo.get('html_url', ''),
                'clone_url': repo.get('clone_url', ''),
                'is_fork': repo.get('fork', False),
                'is_private': repo.get('private', False),
                'has_issues': repo.get('has_issues', False),
                'has_projects': repo.get('has_projects', False),
                'has_wiki': repo.get('has_wiki', False),
                'has_pages': repo.get('has_pages', False),
                'open_issues_count': repo.get('open_issues_count', 0),
                'topics': repo.get('topics', []),
                'license': repo.get('license', {}).get('name', '') if repo.get('license') else '',
                'default_branch': repo.get('default_branch', 'main'),
                'archived': repo.get('archived', False),
                'disabled': repo.get('disabled', False)
            }

            # Calculate repository impact score
            repo_analysis['impact_score'] = self._calculate_repo_impact(repo_analysis)

            # Determine project type
            repo_analysis['project_type'] = self._classify_project_type(repo_analysis)

            # Assess maintenance level
            repo_analysis['maintenance_level'] = self._assess_maintenance_level(repo_analysis)

            processed_repos.append(repo_analysis)

        return processed_repos

    def _process_repositories_optimized(self, repos_data: List[Dict]) -> List[Dict]:
        """Process repositories with memory optimization (fallback method)"""

        # Limit and optimize repository data
        processed_repos = []

        for repo in repos_data[:5]:  # Top 5 repos only
            processed_repos.append({
                'name': repo.get('name', ''),
                'description': (repo.get('description', '') or '')[:200],  # Limit description
                'language': repo.get('language', ''),
                'stars': repo.get('stargazers_count', 0),
                'forks': repo.get('forks_count', 0),
                'updated_at': repo.get('updated_at', ''),
                'url': repo.get('html_url', '')
            })

        return processed_repos
    
    def _extract_languages_comprehensive(self, repos_data: List[Dict]) -> Dict[str, Any]:
        """Extract programming languages with comprehensive analysis"""

        language_stats = {}
        total_repos = len(repos_data)

        for repo in repos_data:
            lang = repo.get('language')
            if lang:
                if lang not in language_stats:
                    language_stats[lang] = {
                        'count': 0,
                        'total_stars': 0,
                        'total_forks': 0,
                        'repositories': []
                    }

                language_stats[lang]['count'] += 1
                language_stats[lang]['total_stars'] += repo.get('stargazers_count', 0)
                language_stats[lang]['total_forks'] += repo.get('forks_count', 0)
                language_stats[lang]['repositories'].append(repo.get('name', ''))

        # Calculate percentages and rankings
        for lang, stats in language_stats.items():
            stats['percentage'] = (stats['count'] / total_repos) * 100 if total_repos > 0 else 0
            stats['avg_stars'] = stats['total_stars'] / stats['count'] if stats['count'] > 0 else 0
            stats['avg_forks'] = stats['total_forks'] / stats['count'] if stats['count'] > 0 else 0

        # Sort by usage frequency
        sorted_languages = sorted(language_stats.items(), key=lambda x: x[1]['count'], reverse=True)

        return {
            'primary_languages': [lang for lang, _ in sorted_languages[:5]],
            'language_distribution': dict(sorted_languages[:10]),
            'total_languages': len(language_stats),
            'most_starred_language': max(language_stats.items(), key=lambda x: x[1]['total_stars'])[0] if language_stats else None,
            'language_diversity_score': min(len(language_stats) / 10.0, 1.0)  # Normalized diversity score
        }

    def _extract_languages_optimized(self, repos_data: List[Dict]) -> List[str]:
        """Extract programming languages efficiently (fallback method)"""

        languages = set()
        for repo in repos_data:
            lang = repo.get('language')
            if lang:
                languages.add(lang)

        return list(languages)[:10]  # Limit to 10 languages
    
    def _determine_activity_level_comprehensive(self, repos_data: List[Dict], contribution_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive activity level determination"""

        if not repos_data:
            return {
                'level': 'inactive',
                'score': 0,
                'factors': {
                    'repository_count': 0,
                    'total_stars': 0,
                    'recent_activity': 0,
                    'collaboration_level': 0
                }
            }

        # Calculate various activity metrics
        total_stars = sum(repo.get('stargazers_count', 0) for repo in repos_data)
        total_forks = sum(repo.get('forks_count', 0) for repo in repos_data)
        repo_count = len(repos_data)
        recent_activity = contribution_data.get('recent_activity_count', 0)
        collaboration_ratio = contribution_data.get('collaboration_ratio', 0)

        # Calculate activity score (0-100)
        activity_score = 0

        # Repository count factor (0-25 points)
        if repo_count >= 20:
            activity_score += 25
        elif repo_count >= 10:
            activity_score += 20
        elif repo_count >= 5:
            activity_score += 15
        elif repo_count >= 2:
            activity_score += 10
        elif repo_count >= 1:
            activity_score += 5

        # Stars factor (0-25 points)
        if total_stars >= 100:
            activity_score += 25
        elif total_stars >= 50:
            activity_score += 20
        elif total_stars >= 20:
            activity_score += 15
        elif total_stars >= 5:
            activity_score += 10
        elif total_stars >= 1:
            activity_score += 5

        # Recent activity factor (0-25 points)
        if recent_activity >= 30:
            activity_score += 25
        elif recent_activity >= 20:
            activity_score += 20
        elif recent_activity >= 10:
            activity_score += 15
        elif recent_activity >= 5:
            activity_score += 10
        elif recent_activity >= 1:
            activity_score += 5

        # Collaboration factor (0-25 points)
        collaboration_score = int(collaboration_ratio * 25)
        activity_score += collaboration_score

        # Determine activity level
        if activity_score >= 80:
            level = "highly_active"
        elif activity_score >= 60:
            level = "very_active"
        elif activity_score >= 40:
            level = "active"
        elif activity_score >= 20:
            level = "moderate"
        elif activity_score >= 5:
            level = "low"
        else:
            level = "inactive"

        return {
            'level': level,
            'score': activity_score,
            'factors': {
                'repository_count': repo_count,
                'total_stars': total_stars,
                'total_forks': total_forks,
                'recent_activity': recent_activity,
                'collaboration_level': collaboration_ratio
            },
            'assessment': self._get_activity_assessment(level, activity_score)
        }

    def _determine_activity_level_fast(self, repos_data: List[Dict]) -> str:
        """Fast activity level determination (fallback method)"""

        if not repos_data:
            return "inactive"

        total_stars = sum(repo.get('stargazers_count', 0) for repo in repos_data)
        repo_count = len(repos_data)

        if total_stars >= 50 and repo_count >= 5:
            return "very_active"
        elif total_stars >= 10 or repo_count >= 3:
            return "active"
        elif repo_count >= 1:
            return "moderate"
        else:
            return "low"
    
    def _is_relevant_result_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> bool:
        """Fast relevance check"""
        
        url = result.get('url', '').lower()
        title = result.get('title', '').lower()
        
        # Quick irrelevant domain filter
        irrelevant_domains = ['facebook.com', 'instagram.com', 'twitter.com', 'pinterest.com']
        if any(domain in url for domain in irrelevant_domains):
            return False
        
        # Quick name check
        candidate_name_lower = candidate.full_name.lower()
        return candidate_name_lower in title or candidate_name_lower in result.get('description', '').lower()
    
    def _process_web_result_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Fast web result processing"""
        
        try:
            return {
                'title': result.get('title', '')[:200],  # Limit title length
                'url': result.get('url', ''),
                'description': result.get('description', '')[:300],  # Limit description
                'platform': self._identify_platform_fast(result.get('url', '')),
                'type': 'web_result',
                'relevance_score': self._calculate_relevance_fast(result, candidate)
            }
        except Exception as e:
            logger.error(f"Web result processing error: {e}")
            return None
    
    def _identify_platform_fast(self, url: str) -> str:
        """Fast platform identification"""
        
        url_lower = url.lower()
        
        platform_map = {
            'linkedin.com': 'linkedin',
            'github.com': 'github',
            'stackoverflow.com': 'stackoverflow',
            'medium.com': 'medium',
            'dev.to': 'dev_to'
        }
        
        for domain, platform in platform_map.items():
            if domain in url_lower:
                return platform
        
        return 'unknown'
    
    def _calculate_relevance_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> float:
        """Fast relevance calculation"""
        
        title = result.get('title', '').lower()
        description = result.get('description', '').lower()
        candidate_name = candidate.full_name.lower()
        
        score = 0.0
        
        # Name matching
        if candidate_name in title:
            score += 0.5
        elif candidate_name in description:
            score += 0.3
        
        # Professional context
        professional_keywords = ['developer', 'engineer', 'programmer', 'manager']
        if any(keyword in title or keyword in description for keyword in professional_keywords):
            score += 0.2
        
        return min(score, 1.0)
    
    async def _generate_research_summary_cached(self, candidate: CandidateProfile, 
                                              research_result: ResearchResult) -> str:
        """Generate cached research summary"""
        
        # Create cache key
        cache_key = f"summary_{candidate.get_candidate_id()}_{research_result.total_results_found}"
        
        # Check cache
        cached_summary = self.summary_cache.get(cache_key)
        if cached_summary:
            self.cache_hits += 1
            return cached_summary
        
        self.cache_misses += 1
        
        try:
            # Generate quick summary without AI for performance
            summary_parts = []
            
            if research_result.linkedin_profile:
                summary_parts.append("LinkedIn profile verified")
            
            if research_result.github_profile:
                github = research_result.github_profile
                repos = github.get('public_repos', 0)
                stars = github.get('total_stars', 0)
                if repos > 0:
                    summary_parts.append(f"GitHub: {repos} repositories, {stars} stars")
                else:
                    summary_parts.append("GitHub profile found")
            
            if research_result.additional_profiles:
                summary_parts.append(f"Additional profiles found: {len(research_result.additional_profiles)}")
            
            if summary_parts:
                summary = f"Research found: {'; '.join(summary_parts)}."
            else:
                summary = "Limited online presence found."
            
            # Cache result
            self.summary_cache.put(cache_key, summary)
            return summary

        except Exception as e:
            logger.error(f"Research summary error: {e}")
            return "Research summary generation failed."

    # Comprehensive analysis methods for GitHub data
    def _calculate_repo_impact(self, repo_data: Dict[str, Any]) -> float:
        """Calculate repository impact score"""

        stars = repo_data.get('stars', 0)
        forks = repo_data.get('forks', 0)
        watchers = repo_data.get('watchers', 0)

        # Weighted impact score
        impact_score = (stars * 0.5) + (forks * 0.3) + (watchers * 0.2)

        # Normalize to 0-100 scale
        return min(impact_score / 10.0, 100.0)

    def _classify_project_type(self, repo_data: Dict[str, Any]) -> str:
        """Classify project type based on repository characteristics"""

        name = repo_data.get('name', '').lower()
        description = repo_data.get('description', '').lower()
        topics = repo_data.get('topics', [])
        language = repo_data.get('language', '').lower()

        # Classification keywords
        if any(keyword in name or keyword in description for keyword in ['api', 'backend', 'server']):
            return 'backend_development'
        elif any(keyword in name or keyword in description for keyword in ['frontend', 'ui', 'react', 'vue', 'angular']):
            return 'frontend_development'
        elif any(keyword in name or keyword in description for keyword in ['ml', 'machine learning', 'ai', 'data science']):
            return 'machine_learning'
        elif any(keyword in name or keyword in description for keyword in ['web', 'website', 'portfolio']):
            return 'web_development'
        elif any(keyword in name or keyword in description for keyword in ['mobile', 'android', 'ios', 'flutter']):
            return 'mobile_development'
        elif any(keyword in name or keyword in description for keyword in ['tool', 'utility', 'script']):
            return 'utility_tool'
        elif any(keyword in name or keyword in description for keyword in ['game', 'gaming']):
            return 'game_development'
        elif language in ['python', 'r', 'julia'] and any(keyword in description for keyword in ['data', 'analysis', 'research']):
            return 'data_analysis'
        else:
            return 'general_development'

    def _assess_maintenance_level(self, repo_data: Dict[str, Any]) -> str:
        """Assess repository maintenance level"""

        from datetime import datetime, timezone
        import dateutil.parser

        try:
            pushed_at = repo_data.get('pushed_at', '')
            if pushed_at:
                last_push = dateutil.parser.parse(pushed_at)
                days_since_push = (datetime.now(timezone.utc) - last_push).days

                if days_since_push <= 30:
                    return 'actively_maintained'
                elif days_since_push <= 90:
                    return 'regularly_maintained'
                elif days_since_push <= 365:
                    return 'occasionally_maintained'
                else:
                    return 'inactive'
            else:
                return 'unknown'
        except:
            return 'unknown'

    def _analyze_project_diversity(self, repos_data: List[Dict]) -> Dict[str, Any]:
        """Analyze diversity of projects"""

        project_types = {}
        languages = set()

        for repo in repos_data:
            project_type = self._classify_project_type(repo)
            project_types[project_type] = project_types.get(project_type, 0) + 1

            lang = repo.get('language')
            if lang:
                languages.add(lang)

        return {
            'project_types': project_types,
            'type_diversity_score': len(project_types) / 8.0,  # Normalized by max expected types
            'language_count': len(languages),
            'most_common_type': max(project_types.items(), key=lambda x: x[1])[0] if project_types else 'unknown'
        }

    def _analyze_collaboration_level(self, repos_data: List[Dict]) -> Dict[str, Any]:
        """Analyze collaboration patterns"""

        total_repos = len(repos_data)
        forked_repos = sum(1 for repo in repos_data if repo.get('is_fork', False))
        repos_with_forks = sum(1 for repo in repos_data if repo.get('forks', 0) > 0)
        repos_with_issues = sum(1 for repo in repos_data if repo.get('has_issues', False))

        return {
            'fork_ratio': forked_repos / total_repos if total_repos > 0 else 0,
            'forked_by_others_ratio': repos_with_forks / total_repos if total_repos > 0 else 0,
            'issues_enabled_ratio': repos_with_issues / total_repos if total_repos > 0 else 0,
            'collaboration_score': ((repos_with_forks + repos_with_issues) / (total_repos * 2)) if total_repos > 0 else 0,
            'assessment': 'high' if repos_with_forks / total_repos > 0.3 else 'medium' if repos_with_forks / total_repos > 0.1 else 'low'
        }

    def _analyze_code_quality(self, repos_data: List[Dict]) -> Dict[str, Any]:
        """Analyze code quality indicators"""

        total_repos = len(repos_data)
        repos_with_readme = sum(1 for repo in repos_data if repo.get('has_wiki', False) or 'readme' in repo.get('name', '').lower())
        repos_with_license = sum(1 for repo in repos_data if repo.get('license', ''))
        repos_with_topics = sum(1 for repo in repos_data if repo.get('topics', []))

        return {
            'documentation_ratio': repos_with_readme / total_repos if total_repos > 0 else 0,
            'license_ratio': repos_with_license / total_repos if total_repos > 0 else 0,
            'topics_ratio': repos_with_topics / total_repos if total_repos > 0 else 0,
            'quality_score': ((repos_with_readme + repos_with_license + repos_with_topics) / (total_repos * 3)) if total_repos > 0 else 0,
            'assessment': 'excellent' if repos_with_license / total_repos > 0.7 else 'good' if repos_with_license / total_repos > 0.4 else 'needs_improvement'
        }

    def _assess_professional_impact(self, profile_data: Dict[str, Any], repos_data: List[Dict]) -> Dict[str, Any]:
        """Assess overall professional impact"""

        followers = profile_data.get('followers', 0)
        total_stars = sum(repo.get('stargazers_count', 0) for repo in repos_data)
        total_forks = sum(repo.get('forks_count', 0) for repo in repos_data)
        public_repos = profile_data.get('public_repos', 0)

        # Calculate impact score
        impact_score = 0

        # Follower impact (0-30 points)
        if followers >= 100:
            impact_score += 30
        elif followers >= 50:
            impact_score += 25
        elif followers >= 20:
            impact_score += 20
        elif followers >= 10:
            impact_score += 15
        elif followers >= 5:
            impact_score += 10
        elif followers >= 1:
            impact_score += 5

        # Star impact (0-40 points)
        if total_stars >= 500:
            impact_score += 40
        elif total_stars >= 200:
            impact_score += 35
        elif total_stars >= 100:
            impact_score += 30
        elif total_stars >= 50:
            impact_score += 25
        elif total_stars >= 20:
            impact_score += 20
        elif total_stars >= 10:
            impact_score += 15
        elif total_stars >= 5:
            impact_score += 10
        elif total_stars >= 1:
            impact_score += 5

        # Repository count impact (0-30 points)
        if public_repos >= 50:
            impact_score += 30
        elif public_repos >= 30:
            impact_score += 25
        elif public_repos >= 20:
            impact_score += 20
        elif public_repos >= 10:
            impact_score += 15
        elif public_repos >= 5:
            impact_score += 10
        elif public_repos >= 1:
            impact_score += 5

        # Determine impact level
        if impact_score >= 80:
            level = 'high_impact'
        elif impact_score >= 60:
            level = 'significant_impact'
        elif impact_score >= 40:
            level = 'moderate_impact'
        elif impact_score >= 20:
            level = 'emerging_impact'
        else:
            level = 'limited_impact'

        return {
            'level': level,
            'score': impact_score,
            'metrics': {
                'followers': followers,
                'total_stars': total_stars,
                'total_forks': total_forks,
                'public_repos': public_repos
            },
            'assessment': self._get_impact_assessment(level, impact_score)
        }

    def _get_activity_assessment(self, level: str, score: int) -> str:
        """Get activity level assessment"""

        assessments = {
            'highly_active': 'Exceptional GitHub activity with consistent contributions and high community engagement',
            'very_active': 'Strong GitHub presence with regular contributions and good community interaction',
            'active': 'Good GitHub activity with steady contributions and moderate engagement',
            'moderate': 'Moderate GitHub presence with occasional contributions',
            'low': 'Limited GitHub activity with infrequent contributions',
            'inactive': 'Minimal or no recent GitHub activity'
        }

        return assessments.get(level, 'Activity level assessment unavailable')

    def _get_impact_assessment(self, level: str, score: int) -> str:
        """Get professional impact assessment"""

        assessments = {
            'high_impact': 'Significant professional impact with strong community recognition and influence',
            'significant_impact': 'Notable professional presence with good community engagement and recognition',
            'moderate_impact': 'Solid professional presence with moderate community engagement',
            'emerging_impact': 'Developing professional presence with growing community recognition',
            'limited_impact': 'Early-stage professional presence with limited community engagement'
        }

        return assessments.get(level, 'Professional impact assessment unavailable')

    async def _research_publications_and_projects(self, candidate: CandidateProfile) -> List[Dict[str, Any]]:
        """Optimized research for publications, research papers, and project portfolios"""

        try:
            results = []

            # Simplified and focused search queries to reduce timeout risk
            focused_queries = [
                f'"{candidate.full_name}" research publication',
                f'"{candidate.full_name}" project portfolio',
                f'"{candidate.full_name}" certification achievement'
            ]

            # Limit to 3 focused queries for performance
            for query in focused_queries:
                try:
                    if self.rate_limiter:
                        await self.rate_limiter.wait("web_search")

                    # Use shorter timeout for publications search
                    search_results = await asyncio.wait_for(
                        self._search_web_cached(query, num_results=2),
                        timeout=10  # Shorter timeout
                    )

                    for result in search_results:
                        # Quick classification
                        result_type = self._classify_research_result_fast(result)

                        if result_type != 'irrelevant':
                            enhanced_result = {
                                **result,
                                'research_type': result_type,
                                'relevance_score': self._calculate_research_relevance_fast(result, candidate),
                                'search_query': query
                            }
                            results.append(enhanced_result)

                    # Early exit if we have enough results
                    if len(results) >= 6:
                        break

                except asyncio.TimeoutError:
                    logger.warning(f"Publications search timeout for query: {query}")
                    continue
                except Exception as e:
                    logger.warning(f"Publications search error for query '{query}': {e}")
                    continue

            # Sort by relevance and return top results
            results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            return results[:6]  # Reduced result count

        except Exception as e:
            logger.error(f"Publications research error: {e}")
            return []

    def _classify_research_result_fast(self, result: Dict[str, Any]) -> str:
        """Fast research result classification"""

        title = result.get('title', '').lower()
        description = result.get('description', '').lower()
        url = result.get('url', '').lower()

        # Quick classification with fewer checks
        if any(keyword in title or keyword in description for keyword in [
            'paper', 'journal', 'research', 'publication'
        ]):
            return 'academic_publication'
        elif any(keyword in title or keyword in description for keyword in [
            'project', 'portfolio', 'github'
        ]):
            return 'project_portfolio'
        elif any(keyword in title or keyword in description for keyword in [
            'certification', 'award', 'achievement'
        ]):
            return 'certification_achievement'
        elif any(keyword in url for keyword in [
            'linkedin', 'github'
        ]):
            return 'professional_profile'
        else:
            return 'general_mention'

    def _calculate_research_relevance_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> float:
        """Fast relevance calculation for research results"""

        title = result.get('title', '').lower()
        description = result.get('description', '').lower()

        relevance_score = 0.0

        # Name matching (high weight)
        if candidate.full_name.lower() in title:
            relevance_score += 0.5
        elif candidate.full_name.lower() in description:
            relevance_score += 0.3

        # Quick domain relevance check
        if any(keyword in title or keyword in description for keyword in [
            'data science', 'machine learning', 'ai', 'research'
        ]):
            relevance_score += 0.2

        return min(relevance_score, 1.0)

    def _classify_research_result(self, result: Dict[str, Any]) -> str:
        """Classify research result type"""

        title = result.get('title', '').lower()
        description = result.get('description', '').lower()
        url = result.get('url', '').lower()

        # Academic publications
        if any(keyword in title or keyword in description for keyword in [
            'paper', 'journal', 'conference', 'proceedings', 'publication', 'research',
            'ieee', 'acm', 'springer', 'elsevier', 'arxiv', 'researchgate'
        ]):
            return 'academic_publication'

        # Project portfolios
        elif any(keyword in title or keyword in description for keyword in [
            'project', 'portfolio', 'github', 'demo', 'application', 'system', 'tool'
        ]):
            return 'project_portfolio'

        # Certifications and achievements
        elif any(keyword in title or keyword in description for keyword in [
            'certification', 'certificate', 'award', 'achievement', 'recognition',
            'winner', 'competition', 'hackathon', 'contest'
        ]):
            return 'certification_achievement'

        # Professional profiles
        elif any(keyword in url for keyword in [
            'linkedin', 'github', 'stackoverflow', 'medium', 'dev.to', 'kaggle'
        ]):
            return 'professional_profile'

        # News and articles
        elif any(keyword in title or keyword in description for keyword in [
            'interview', 'article', 'news', 'feature', 'spotlight', 'profile'
        ]):
            return 'media_coverage'

        else:
            return 'general_mention'

    def _calculate_research_relevance(self, result: Dict[str, Any], candidate: CandidateProfile) -> float:
        """Calculate relevance score for research result"""

        title = result.get('title', '').lower()
        description = result.get('description', '').lower()
        url = result.get('url', '').lower()

        relevance_score = 0.0

        # Name matching (high weight)
        if candidate.full_name.lower() in title:
            relevance_score += 0.4
        elif candidate.full_name.lower() in description:
            relevance_score += 0.3

        # Technical skills matching
        candidate_skills = (
            list(candidate.technical_skills) +
            list(candidate.programming_languages) +
            list(candidate.frameworks)
        )

        skill_matches = sum(1 for skill in candidate_skills
                          if skill.lower() in title or skill.lower() in description)
        relevance_score += min(skill_matches * 0.05, 0.2)

        # Domain relevance
        domain_keywords = ['data science', 'machine learning', 'ai', 'artificial intelligence',
                          'analytics', 'python', 'research', 'computer science']

        domain_matches = sum(1 for keyword in domain_keywords
                           if keyword in title or keyword in description)
        relevance_score += min(domain_matches * 0.03, 0.15)

        # Source credibility
        credible_sources = ['ieee', 'acm', 'springer', 'github', 'linkedin', 'researchgate',
                           'arxiv', 'scholar.google', 'university', 'edu']

        if any(source in url for source in credible_sources):
            relevance_score += 0.1

        # Result type bonus
        result_type = result.get('research_type', '')
        type_bonuses = {
            'academic_publication': 0.15,
            'project_portfolio': 0.12,
            'certification_achievement': 0.10,
            'professional_profile': 0.08,
            'media_coverage': 0.05
        }

        relevance_score += type_bonuses.get(result_type, 0.0)

        return min(relevance_score, 1.0)  # Cap at 1.0

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "request_times": self.request_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "search_cache_size": self.search_cache.size(),
                "profile_cache_size": self.profile_cache.size(),
                "summary_cache_size": self.summary_cache.size()
            },
            "failed_requests": self.failed_requests,
            "average_request_time": sum(self.request_times.values()) / len(self.request_times) if self.request_times else 0
        }
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.search_cache.clear()
        self.profile_cache.clear()
        self.summary_cache.clear()
        
        gc.collect()

# Export optimized components
__all__ = ['OptimizedEnhancedResearchAgent']