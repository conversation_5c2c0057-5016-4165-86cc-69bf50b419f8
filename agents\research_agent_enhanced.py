# research_agent_enhanced_optimized.py
import asyncio
import aiohttp
import logging
import json
import re
import time
import gc
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import quote, urljoin
from bs4 import BeautifulSoup
import weakref

from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, ResearchResult, log_agent_message, 
    update_processing_stats, LRUCache, MemoryMonitor
)

logger = logging.getLogger(__name__)

class OptimizedEnhancedResearchAgent:
    """
    High-performance research agent with:
    - Async parallel requests with connection pooling
    - Intelligent caching with TTL
    - Adaptive rate limiting
    - Memory-optimized data structures
    - Batch processing
    """
    
    def __init__(self, openai_api_key: str, serp_api_key: str = None, 
                 tavily_api_key: str = None, session=None, rate_limiter=None, cache=None):
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.session = session
        self.rate_limiter = rate_limiter
        self.cache = cache or LRUCache(maxsize=500)
        
        # Performance settings
        self.max_concurrent_requests = 5
        self.max_results_per_platform = 3  # Reduced for performance
        self.request_timeout = 15  # Reduced timeout
        self.batch_size = 3  # Process 3 candidates at once
        
        # Research platforms (optimized list)
        self.research_platforms = [
            "linkedin",
            "github", 
            "personal_websites",
            "stackoverflow",
            "medium"
        ]
        
        # Caching with TTL for different data types
        self.search_cache = LRUCache(maxsize=200)  # Search results
        self.profile_cache = LRUCache(maxsize=100)  # Profile data
        self.summary_cache = LRUCache(maxsize=50)   # AI summaries
        
        # Performance tracking
        self.request_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        self.failed_requests = 0
        
        # Rate limiting counters
        self.request_counts = {}
        self.last_request_times = {}
        
    async def research_candidates_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Main optimized research method with parallel processing"""
        
        try:
            logger.info("🔍 Starting high-performance candidate research")
            state = log_agent_message(state, "research", "Starting optimized candidate research", "info")
            
            start_time = time.time()
            
            if not state.get("matched_candidates"):
                state["errors"].append("No matched candidates available for research")
                return state
            
            # Get candidate profiles for research
            candidate_profiles = {
                f"{c.full_name}_{c.cv_filename}": c 
                for c in state.get("parsed_candidates", [])
            }
            
            # Filter to only research matched candidates
            research_targets = []
            for match_result in state["matched_candidates"]:
                candidate = candidate_profiles.get(match_result.candidate_id)
                if candidate:
                    research_targets.append((candidate, match_result.match_score))
            
            if not research_targets:
                state["warnings"].append("No valid candidates found for research")
                return state
            
            # Sort by match score - research best candidates first
            research_targets.sort(key=lambda x: x[1], reverse=True)
            
            # Process candidates in optimized batches
            research_results = await self._research_candidates_batch(research_targets)
            
            state["researched_candidates"] = research_results
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "research",
                candidates_researched=len(research_results),
                total_research_results=sum(r.total_results_found for r in research_results),
                platforms_searched=len(self.research_platforms),
                processing_time=total_time,
                requests_per_second=len(research_targets) * len(self.research_platforms) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                failed_requests=self.failed_requests,
                parallel_processing=True,
                adaptive_rate_limiting=True
            )
            
            logger.info(f"🎯 Research completed in {total_time:.2f}s for {len(research_results)} candidates")
            logger.info(f"💾 Cache hit ratio: {self.cache_hits/(self.cache_hits + self.cache_misses)*100:.1f}%")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized research process failed: {e}")
            state["errors"].append(f"Research process failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    async def _research_candidates_batch(self, research_targets: List[Tuple]) -> List[ResearchResult]:
        """Research candidates in optimized batches"""
        
        results = []
        
        # Process in small batches to manage resources
        for i in range(0, len(research_targets), self.batch_size):
            batch = research_targets[i:i + self.batch_size]
            
            logger.info(f"🔄 Researching batch {i//self.batch_size + 1}/{(len(research_targets) + self.batch_size - 1)//self.batch_size}")
            
            # Create batch tasks
            batch_tasks = []
            for candidate, score in batch:
                task = asyncio.create_task(
                    self._research_single_candidate_optimized(candidate)
                )
                batch_tasks.append(task)
            
            # Execute batch with timeout
            try:
                batch_results = await asyncio.wait_for(
                    asyncio.gather(*batch_tasks, return_exceptions=True),
                    timeout=60  # 60 second timeout per batch
                )
                
                # Process results
                for result in batch_results:
                    if isinstance(result, ResearchResult):
                        results.append(result)
                    elif isinstance(result, Exception):
                        logger.error(f"Batch research error: {result}")
                
            except asyncio.TimeoutError:
                logger.warning("Research batch timeout - continuing with partial results")
            
            # Adaptive delay between batches
            if i + self.batch_size < len(research_targets):
                await asyncio.sleep(1.0)
            
            # Memory cleanup between batches
            gc.collect()
        
        return results
    
    async def _research_single_candidate_optimized(self, candidate: CandidateProfile) -> ResearchResult:
        """Optimized research for single candidate"""
        
        research_result = ResearchResult(
            candidate_id=candidate.get_candidate_id(),
            platforms_searched=tuple()
        )
        
        try:
            platforms_found = []
            
            # Create concurrent research tasks with limits
            research_tasks = []
            
            # 1. LinkedIn research (highest priority)
            task = asyncio.create_task(
                self._research_linkedin_optimized(candidate)
            )
            research_tasks.append(('linkedin', task))
            
            # 2. GitHub research (high priority)
            task = asyncio.create_task(
                self._research_github_optimized(candidate)
            )
            research_tasks.append(('github', task))
            
            # 3. General web search (medium priority)
            task = asyncio.create_task(
                self._research_general_web_optimized(candidate)
            )
            research_tasks.append(('general_web', task))
            
            # Execute tasks with controlled concurrency
            completed_tasks = 0
            
            for platform, task in research_tasks:
                try:
                    # Add adaptive delay between requests
                    if self.rate_limiter:
                        await self.rate_limiter.wait(f"research_{platform}")
                    
                    result = await asyncio.wait_for(task, timeout=self.request_timeout)
                    
                    if result:
                        if platform == 'linkedin' and result:
                            research_result.linkedin_profile = result
                            platforms_found.append(platform)
                        elif platform == 'github' and result:
                            research_result.github_profile = result
                            platforms_found.append(platform)
                        elif platform == 'general_web' and result:
                            research_result.additional_profiles = tuple(result)
                            platforms_found.append(platform)
                    
                    completed_tasks += 1
                    
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout for {platform} research on {candidate.full_name}")
                except Exception as e:
                    logger.error(f"Error in {platform} research: {e}")
                    self.failed_requests += 1
            
            # Set platforms searched
            research_result.platforms_searched = tuple(platforms_found)
            
            # Calculate total results
            research_result.total_results_found = (
                (1 if research_result.linkedin_profile else 0) +
                (1 if research_result.github_profile else 0) +
                len(research_result.additional_profiles)
            )
            
            # Generate summary only if we found results
            if research_result.total_results_found > 0:
                research_result.research_summary = await self._generate_research_summary_cached(
                    candidate, research_result
                )
            else:
                research_result.research_summary = "Limited online presence found."
            
            return research_result
            
        except Exception as e:
            logger.error(f"Single candidate research error for {candidate.full_name}: {e}")
            return research_result
    
    async def _research_linkedin_optimized(self, candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Optimized LinkedIn research with caching"""
        
        # Create cache key
        cache_key = f"linkedin_{candidate.full_name.lower().replace(' ', '_')}"
        
        # Check cache first
        cached_result = self.profile_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            # Use direct URL if available
            if candidate.linkedin_url:
                profile_data = await self._extract_linkedin_profile_optimized(candidate.linkedin_url)
                if profile_data:
                    self.profile_cache.put(cache_key, profile_data)
                    return profile_data
            
            # Search for LinkedIn profile
            search_queries = [
                f'"{candidate.full_name}" linkedin',
                f'{candidate.full_name} linkedin {candidate.current_company}' if candidate.current_company else None
            ]
            
            search_queries = [q for q in search_queries if q]
            
            for query in search_queries[:2]:  # Limit queries
                results = await self._search_web_cached(query, num_results=2)
                
                for result in results:
                    if 'linkedin.com/in/' in result.get('url', ''):
                        profile_data = await self._extract_linkedin_profile_optimized(result['url'])
                        if profile_data:
                            self.profile_cache.put(cache_key, profile_data)
                            return profile_data
            
            return None
            
        except Exception as e:
            logger.error(f"LinkedIn research error: {e}")
            return None
    
    async def _research_github_optimized(self, candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Optimized GitHub research with API caching"""
        
        # Create cache key
        cache_key = f"github_{candidate.full_name.lower().replace(' ', '_')}"
        
        # Check cache first
        cached_result = self.profile_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            # Try direct GitHub URL first
            if candidate.github_url:
                github_data = await self._extract_github_profile_api(candidate.github_url)
                if github_data:
                    self.profile_cache.put(cache_key, github_data)
                    return github_data
            
            # Search for GitHub profile
            search_queries = [
                f'"{candidate.full_name}" github',
                f'site:github.com {candidate.full_name}'
            ]
            
            for query in search_queries[:1]:  # Only first query for performance
                results = await self._search_web_cached(query, num_results=3)
                
                for result in results:
                    url = result.get('url', '')
                    if 'github.com/' in url and '/repositories' not in url:
                        github_data = await self._extract_github_profile_api(url)
                        if github_data:
                            self.profile_cache.put(cache_key, github_data)
                            return github_data
            
            return None
            
        except Exception as e:
            logger.error(f"GitHub research error: {e}")
            return None
    
    async def _research_general_web_optimized(self, candidate: CandidateProfile) -> List[Dict[str, Any]]:
        """Optimized general web research"""
        
        # Create cache key
        cache_key = f"web_{candidate.full_name.lower().replace(' ', '_')}"
        
        # Check cache first
        cached_result = self.search_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            results = []
            
            # Focused search queries
            search_queries = [
                f'"{candidate.full_name}" {candidate.current_position}' if candidate.current_position else f'"{candidate.full_name}"',
                f'{candidate.full_name} portfolio'
            ]
            
            for query in search_queries[:1]:  # Limit for performance
                search_results = await self._search_web_cached(query, num_results=3)
                
                for result in search_results:
                    if self._is_relevant_result_fast(result, candidate):
                        processed_result = self._process_web_result_fast(result, candidate)
                        if processed_result:
                            results.append(processed_result)
            
            # Cache and return
            limited_results = results[:5]  # Limit results
            self.search_cache.put(cache_key, limited_results)
            return limited_results
            
        except Exception as e:
            logger.error(f"General web research error: {e}")
            return []
    
    async def _search_web_cached(self, query: str, num_results: int = 3) -> List[Dict[str, Any]]:
        """Cached web search with fallback methods"""
        
        # Create cache key
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        cache_key = f"search_{query_hash}_{num_results}"
        
        # Check cache
        cached_result = self.search_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        try:
            # Try available search APIs
            if self.tavily_api_key:
                results = await self._search_with_tavily_optimized(query, num_results)
            elif self.serp_api_key:
                results = await self._search_with_serp_optimized(query, num_results)
            else:
                results = self._search_fallback_optimized(query, num_results)
            
            # Cache results
            self.search_cache.put(cache_key, results)
            return results
            
        except Exception as e:
            logger.error(f"Web search error for '{query}': {e}")
            return []
    
    async def _search_with_tavily_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Optimized Tavily search"""
        
        try:
            import tavily
            
            client = tavily.TavilyClient(api_key=self.tavily_api_key)
            
            # Use async if available, otherwise sync
            if hasattr(client, 'search_async'):
                response = await client.search_async(query, max_results=num_results)
            else:
                response = client.search(query, max_results=num_results)
            
            results = []
            for item in response.get('results', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('url', ''),
                    'description': item.get('content', '')[:500]  # Limit description length
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            return []
    
    async def _search_with_serp_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Optimized SerpAPI search"""
        
        try:
            if not self.session:
                return []
            
            url = "https://serpapi.com/search"
            params = {
                "q": query,
                "api_key": self.serp_api_key,
                "num": num_results,
                "hl": "en"
            }
            
            # Rate limiting
            if self.rate_limiter:
                await self.rate_limiter.wait("serp_api")
            
            async with self.session.get(url, params=params, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    for item in data.get('organic_results', []):
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'description': item.get('snippet', '')[:500]
                        })
                    
                    return results
                else:
                    logger.error(f"SerpAPI error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"SerpAPI search error: {e}")
            return []
    
    def _search_fallback_optimized(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """Optimized fallback search"""
        
        logger.warning("Using fallback search - limited results")
        
        # Return minimal fallback result
        return [{
            'title': f"Search result for: {query}",
            'url': "https://example.com/search-limited",
            'description': "Limited search capability - add API keys for better results"
        }]
    
    async def _extract_linkedin_profile_optimized(self, url: str) -> Optional[Dict[str, Any]]:
        """Optimized LinkedIn profile extraction"""
        
        try:
            # LinkedIn has anti-scraping - return basic info
            return {
                'url': url,
                'platform': 'linkedin',
                'name': 'Profile found',
                'headline': 'Limited access due to LinkedIn restrictions',
                'location': 'See LinkedIn profile',
                'note': 'LinkedIn profile identified'
            }
            
        except Exception as e:
            logger.error(f"LinkedIn extraction error: {e}")
            return None
    
    async def _extract_github_profile_api(self, url: str) -> Optional[Dict[str, Any]]:
        """Optimized GitHub profile extraction using API"""
        
        try:
            # Extract username from URL
            username_match = re.search(r'github\.com/([^/]+)', url)
            if not username_match:
                return None
            
            username = username_match.group(1)
            
            # Use GitHub API
            if not self.session:
                return None
            
            # Rate limiting
            if self.rate_limiter:
                await self.rate_limiter.wait("github_api")
            
            # Get profile info
            api_url = f"https://api.github.com/users/{username}"
            
            async with self.session.get(api_url, timeout=10) as response:
                if response.status == 200:
                    profile_data = await response.json()
                    
                    # Get top repositories (limited for performance)
                    repos_url = f"https://api.github.com/users/{username}/repos?sort=updated&per_page=10"
                    
                    try:
                        async with self.session.get(repos_url, timeout=10) as repos_response:
                            if repos_response.status == 200:
                                repos_data = await repos_response.json()
                            else:
                                repos_data = []
                    except:
                        repos_data = []
                    
                    # Build optimized profile
                    return {
                        'url': url,
                        'platform': 'github',
                        'username': username,
                        'name': profile_data.get('name', ''),
                        'bio': profile_data.get('bio', ''),
                        'location': profile_data.get('location', ''),
                        'public_repos': profile_data.get('public_repos', 0),
                        'followers': profile_data.get('followers', 0),
                        'following': profile_data.get('following', 0),
                        'created_at': profile_data.get('created_at', ''),
                        'repositories': self._process_repositories_optimized(repos_data),
                        'languages': self._extract_languages_optimized(repos_data),
                        'total_stars': sum(repo.get('stargazers_count', 0) for repo in repos_data),
                        'activity_level': self._determine_activity_level_fast(repos_data)
                    }
                else:
                    logger.warning(f"GitHub API error: {response.status}")
                    return None
            
        except Exception as e:
            logger.error(f"GitHub API extraction error: {e}")
            return None
    
    def _process_repositories_optimized(self, repos_data: List[Dict]) -> List[Dict]:
        """Process repositories with memory optimization"""
        
        # Limit and optimize repository data
        processed_repos = []
        
        for repo in repos_data[:5]:  # Top 5 repos only
            processed_repos.append({
                'name': repo.get('name', ''),
                'description': (repo.get('description', '') or '')[:200],  # Limit description
                'language': repo.get('language', ''),
                'stars': repo.get('stargazers_count', 0),
                'forks': repo.get('forks_count', 0),
                'updated_at': repo.get('updated_at', ''),
                'url': repo.get('html_url', '')
            })
        
        return processed_repos
    
    def _extract_languages_optimized(self, repos_data: List[Dict]) -> List[str]:
        """Extract programming languages efficiently"""
        
        languages = set()
        for repo in repos_data:
            lang = repo.get('language')
            if lang:
                languages.add(lang)
        
        return list(languages)[:10]  # Limit to 10 languages
    
    def _determine_activity_level_fast(self, repos_data: List[Dict]) -> str:
        """Fast activity level determination"""
        
        if not repos_data:
            return "inactive"
        
        total_stars = sum(repo.get('stargazers_count', 0) for repo in repos_data)
        repo_count = len(repos_data)
        
        if total_stars >= 50 and repo_count >= 5:
            return "very_active"
        elif total_stars >= 10 or repo_count >= 3:
            return "active"
        elif repo_count >= 1:
            return "moderate"
        else:
            return "low"
    
    def _is_relevant_result_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> bool:
        """Fast relevance check"""
        
        url = result.get('url', '').lower()
        title = result.get('title', '').lower()
        
        # Quick irrelevant domain filter
        irrelevant_domains = ['facebook.com', 'instagram.com', 'twitter.com', 'pinterest.com']
        if any(domain in url for domain in irrelevant_domains):
            return False
        
        # Quick name check
        candidate_name_lower = candidate.full_name.lower()
        return candidate_name_lower in title or candidate_name_lower in result.get('description', '').lower()
    
    def _process_web_result_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> Optional[Dict[str, Any]]:
        """Fast web result processing"""
        
        try:
            return {
                'title': result.get('title', '')[:200],  # Limit title length
                'url': result.get('url', ''),
                'description': result.get('description', '')[:300],  # Limit description
                'platform': self._identify_platform_fast(result.get('url', '')),
                'type': 'web_result',
                'relevance_score': self._calculate_relevance_fast(result, candidate)
            }
        except Exception as e:
            logger.error(f"Web result processing error: {e}")
            return None
    
    def _identify_platform_fast(self, url: str) -> str:
        """Fast platform identification"""
        
        url_lower = url.lower()
        
        platform_map = {
            'linkedin.com': 'linkedin',
            'github.com': 'github',
            'stackoverflow.com': 'stackoverflow',
            'medium.com': 'medium',
            'dev.to': 'dev_to'
        }
        
        for domain, platform in platform_map.items():
            if domain in url_lower:
                return platform
        
        return 'unknown'
    
    def _calculate_relevance_fast(self, result: Dict[str, Any], candidate: CandidateProfile) -> float:
        """Fast relevance calculation"""
        
        title = result.get('title', '').lower()
        description = result.get('description', '').lower()
        candidate_name = candidate.full_name.lower()
        
        score = 0.0
        
        # Name matching
        if candidate_name in title:
            score += 0.5
        elif candidate_name in description:
            score += 0.3
        
        # Professional context
        professional_keywords = ['developer', 'engineer', 'programmer', 'manager']
        if any(keyword in title or keyword in description for keyword in professional_keywords):
            score += 0.2
        
        return min(score, 1.0)
    
    async def _generate_research_summary_cached(self, candidate: CandidateProfile, 
                                              research_result: ResearchResult) -> str:
        """Generate cached research summary"""
        
        # Create cache key
        cache_key = f"summary_{candidate.get_candidate_id()}_{research_result.total_results_found}"
        
        # Check cache
        cached_summary = self.summary_cache.get(cache_key)
        if cached_summary:
            self.cache_hits += 1
            return cached_summary
        
        self.cache_misses += 1
        
        try:
            # Generate quick summary without AI for performance
            summary_parts = []
            
            if research_result.linkedin_profile:
                summary_parts.append("LinkedIn profile verified")
            
            if research_result.github_profile:
                github = research_result.github_profile
                repos = github.get('public_repos', 0)
                stars = github.get('total_stars', 0)
                if repos > 0:
                    summary_parts.append(f"GitHub: {repos} repositories, {stars} stars")
                else:
                    summary_parts.append("GitHub profile found")
            
            if research_result.additional_profiles:
                summary_parts.append(f"Additional profiles found: {len(research_result.additional_profiles)}")
            
            if summary_parts:
                summary = f"Research found: {'; '.join(summary_parts)}."
            else:
                summary = "Limited online presence found."
            
            # Cache result
            self.summary_cache.put(cache_key, summary)
            return summary
            
        except Exception as e:
            logger.error(f"Research summary error: {e}")
            return "Research summary generation failed."
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "request_times": self.request_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "search_cache_size": self.search_cache.size(),
                "profile_cache_size": self.profile_cache.size(),
                "summary_cache_size": self.summary_cache.size()
            },
            "failed_requests": self.failed_requests,
            "average_request_time": sum(self.request_times.values()) / len(self.request_times) if self.request_times else 0
        }
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.search_cache.clear()
        self.profile_cache.clear()
        self.summary_cache.clear()
        
        gc.collect()

# Export optimized components
__all__ = ['OptimizedEnhancedResearchAgent']