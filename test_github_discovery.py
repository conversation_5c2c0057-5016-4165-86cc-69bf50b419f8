#!/usr/bin/env python3
"""
Enhanced test script to debug GitHub profile discovery pipeline
"""

import asyncio
import aiohttp
import logging
import os
import sys
import time
from typing import Dict, Any, List

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure detailed logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('github_discovery_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Import project modules
from state_management import CandidateProfile, ThreadSafeState, JobRequirements
from agents.research_agent_enhanced import OptimizedEnhancedResearchAgent

class GitHubDiscoveryTester:
    """Test class for GitHub profile discovery pipeline"""
    
    def __init__(self, github_token: str = None, serp_api_key: str = None, tavily_api_key: str = None):
        self.github_token = github_token
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def create_test_candidates(self) -> List[CandidateProfile]:
        """Create test candidates with known GitHub profiles"""
        
        candidates = [
            # Real GitHub users for testing
            CandidateProfile(
                full_name="Linus Torvalds",
                email="<EMAIL>",
                phone="555-0001",
                current_position="Software Engineer",
                current_company="Linux Foundation",
                skills=["C", "Linux", "Kernel Development"],
                experience_years=30,
                education="Master's in Computer Science",
                cv_filename="linus_torvalds_resume.pdf"
            ),
            CandidateProfile(
                full_name="John Doe",
                email="<EMAIL>",
                phone="555-0002",
                current_position="Data Scientist",
                current_company="Tech Corp",
                skills=["Python", "Machine Learning", "Data Science"],
                experience_years=5,
                education="Bachelor's in Computer Science",
                cv_filename="john_doe_resume.pdf"
            ),
            CandidateProfile(
                full_name="Jane Smith",
                email="<EMAIL>",
                phone="555-0003",
                current_position="Frontend Developer",
                current_company="Web Solutions",
                skills=["JavaScript", "React", "CSS"],
                experience_years=3,
                education="Bachelor's in Web Development",
                cv_filename="jane_smith_resume.pdf"
            )
        ]
        
        return candidates
    
    async def test_search_query_generation(self) -> Dict[str, Any]:
        """Test GitHub search query generation"""
        
        logger.info("=== Testing GitHub Search Query Generation ===")
        
        try:
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",
                github_token=self.github_token,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                session=self.session
            )
            
            candidates = self.create_test_candidates()
            
            results = {}
            for candidate in candidates:
                logger.info(f"🔍 Generating queries for: {candidate.full_name}")
                
                queries = agent._generate_github_search_queries(candidate)
                
                logger.info(f"📝 Generated {len(queries)} queries:")
                for i, query in enumerate(queries, 1):
                    logger.info(f"   {i}. {query}")
                
                results[candidate.full_name] = {
                    'query_count': len(queries),
                    'queries': queries
                }
            
            return {'status': 'success', 'results': results}
            
        except Exception as e:
            logger.error(f"❌ Query generation test error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def test_web_search_functionality(self) -> Dict[str, Any]:
        """Test web search functionality and result processing"""
        
        logger.info("=== Testing Web Search Functionality ===")
        
        try:
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",
                github_token=self.github_token,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                session=self.session
            )
            
            # Test with a known query
            test_query = '"Linus Torvalds" github'
            
            logger.info(f"🔍 Testing search query: {test_query}")
            
            search_results = await agent._search_web_cached(test_query, num_results=5)
            
            logger.info(f"📊 Search returned {len(search_results)} results")
            
            github_urls_found = []
            for i, result in enumerate(search_results, 1):
                url = result.get('url', '')
                title = result.get('title', '')
                description = result.get('description', '')
                
                logger.info(f"🔗 Result {i}:")
                logger.info(f"   URL: {url}")
                logger.info(f"   Title: {title}")
                logger.info(f"   Description: {description[:100]}...")
                
                # Check if it's a GitHub URL
                if agent._is_valid_github_profile_url(url):
                    github_urls_found.append(url)
                    logger.info(f"   ✅ Valid GitHub profile URL")
                else:
                    logger.info(f"   ❌ Not a valid GitHub profile URL")
            
            return {
                'status': 'success',
                'total_results': len(search_results),
                'github_urls_found': len(github_urls_found),
                'github_urls': github_urls_found,
                'search_api_used': 'tavily' if self.tavily_api_key else ('serp' if self.serp_api_key else 'fallback')
            }
            
        except Exception as e:
            logger.error(f"❌ Web search test error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def test_github_url_validation(self) -> Dict[str, Any]:
        """Test GitHub URL validation logic"""
        
        logger.info("=== Testing GitHub URL Validation ===")
        
        try:
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",
                github_token=self.github_token,
                session=self.session
            )
            
            # Test URLs
            test_urls = [
                "https://github.com/torvalds",
                "https://github.com/octocat",
                "https://github.com/torvalds/linux",  # Should be invalid (repo, not profile)
                "https://github.com/search",  # Should be invalid
                "https://github.com/topics",  # Should be invalid
                "https://github.com/john-doe",
                "https://github.com/jane_smith",
                "https://github.com/user123",
                "https://example.com/profile",  # Should be invalid
                "https://github.com/features",  # Should be invalid
            ]
            
            results = {}
            for url in test_urls:
                is_valid = agent._is_valid_github_profile_url(url)
                username = agent._extract_github_username(url)
                
                logger.info(f"🧪 Testing URL: {url}")
                logger.info(f"   Valid: {is_valid}")
                logger.info(f"   Username: {username}")
                
                results[url] = {
                    'is_valid': is_valid,
                    'username': username
                }
            
            return {'status': 'success', 'results': results}
            
        except Exception as e:
            logger.error(f"❌ URL validation test error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def test_full_github_discovery_pipeline(self) -> Dict[str, Any]:
        """Test the complete GitHub discovery pipeline"""
        
        logger.info("=== Testing Full GitHub Discovery Pipeline ===")
        
        try:
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",
                github_token=self.github_token,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                session=self.session
            )
            
            # Clear any negative cache results
            agent.clear_negative_cache_results()
            
            candidates = self.create_test_candidates()
            
            results = {}
            for candidate in candidates:
                logger.info(f"🔍 Testing full pipeline for: {candidate.full_name}")
                
                start_time = time.time()
                github_result = await agent._research_github_optimized(candidate)
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                if github_result:
                    logger.info(f"✅ Found GitHub profile for {candidate.full_name}")
                    logger.info(f"   Username: {github_result.get('username')}")
                    logger.info(f"   Name: {github_result.get('name')}")
                    logger.info(f"   Public repos: {github_result.get('public_repos')}")
                    logger.info(f"   Followers: {github_result.get('followers')}")
                else:
                    logger.warning(f"❌ No GitHub profile found for {candidate.full_name}")
                
                results[candidate.full_name] = {
                    'found': github_result is not None,
                    'processing_time': processing_time,
                    'profile_data': github_result
                }
            
            return {'status': 'success', 'results': results}
            
        except Exception as e:
            logger.error(f"❌ Full pipeline test error: {e}")
            return {'status': 'error', 'error': str(e)}

async def main():
    """Main test function"""
    
    # Get API keys from environment
    github_token = os.getenv('GITHUB_TOKEN')
    serp_api_key = os.getenv('SERP_API_KEY')
    tavily_api_key = os.getenv('TAVILY_API_KEY')
    
    logger.info("🧪 Starting GitHub Discovery Pipeline Tests")
    logger.info(f"GitHub Token: {'✅ Available' if github_token else '❌ Not found'}")
    logger.info(f"SERP API Key: {'✅ Available' if serp_api_key else '❌ Not found'}")
    logger.info(f"Tavily API Key: {'✅ Available' if tavily_api_key else '❌ Not found'}")
    
    async with GitHubDiscoveryTester(github_token, serp_api_key, tavily_api_key) as tester:
        
        # Test 1: Query generation
        query_result = await tester.test_search_query_generation()
        print(f"\n📊 Query Generation Test: {query_result['status']}")
        
        # Test 2: Web search functionality
        search_result = await tester.test_web_search_functionality()
        print(f"\n📊 Web Search Test: {search_result}")
        
        # Test 3: URL validation
        validation_result = await tester.test_github_url_validation()
        print(f"\n📊 URL Validation Test: {validation_result['status']}")
        
        # Test 4: Full pipeline
        pipeline_result = await tester.test_full_github_discovery_pipeline()
        print(f"\n📊 Full Pipeline Test: {pipeline_result}")
        
        # Summary
        print("\n" + "="*60)
        print("🎯 GITHUB DISCOVERY TEST SUMMARY")
        print("="*60)
        
        if pipeline_result.get('status') == 'success':
            results = pipeline_result.get('results', {})
            found_count = sum(1 for r in results.values() if r.get('found'))
            total_count = len(results)
            
            print(f"✅ GitHub profiles found: {found_count}/{total_count}")
            
            for name, result in results.items():
                status = "✅ Found" if result.get('found') else "❌ Not found"
                time_taken = result.get('processing_time', 0)
                print(f"   {name}: {status} ({time_taken:.2f}s)")
        else:
            print(f"❌ Pipeline test failed: {pipeline_result.get('error')}")
        
        print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
