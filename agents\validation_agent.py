# validation_agent_optimized.py
import asyncio
import logging
import json
import re
import gc
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from difflib import SequenceMatcher
from concurrent.futures import ThreadPoolExecutor

from openai import OpenAI
from state_management import (
    ThreadSafeState, CandidateProfile, ResearchResult, ValidationResult,
    log_agent_message, update_processing_stats, LRUCache, BatchProcessor
)

logger = logging.getLogger(__name__)

class OptimizedValidationAgent:
    """
    High-performance validation agent with:
    - Batch processing for efficiency
    - Cached validation results
    - Optimized algorithms
    - Memory-efficient data structures
    - Parallel validation checks
    """
    
    def __init__(self, openai_api_key: str, batch_processor=None, cache=None):
        self.client = OpenAI(api_key=openai_api_key)
        self.model = "gpt-4"
        self.batch_processor = batch_processor or BatchProcessor(batch_size=5, max_workers=3)
        self.cache = cache or LRUCache(maxsize=100)
        
        # Optimized validation criteria with weights
        self.validation_criteria = {
            "name_match": {"threshold": 0.8, "weight": 0.30},
            "email_consistency": {"threshold": 0.7, "weight": 0.15},
            "location_consistency": {"threshold": 0.6, "weight": 0.10},
            "experience_consistency": {"threshold": 0.7, "weight": 0.25},
            "skills_consistency": {"threshold": 0.6, "weight": 0.15},
            "education_consistency": {"threshold": 0.6, "weight": 0.05}
        }
        
        # Caching for validation results
        self.validation_cache = LRUCache(maxsize=200)
        self.analysis_cache = LRUCache(maxsize=50)
        
        # Pre-compiled regex patterns for performance
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.location_patterns = [
            re.compile(r'\b([A-Za-z\s]+),\s*([A-Z]{2})\b'),  # City, State
            re.compile(r'\b([A-Za-z\s]+),\s*([A-Za-z\s]+)\b')  # City, Country
        ]
        
        # Performance tracking
        self.validation_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
    async def validate_candidates_batch(self, state: ThreadSafeState) -> ThreadSafeState:
        """Main batch validation method with optimization"""
        
        try:
            logger.info("✅ Starting high-performance candidate validation")
            state = log_agent_message(state, "validation", "Starting optimized batch validation", "info")
            
            start_time = time.time()
            
            if not state.get("researched_candidates"):
                state["errors"].append("No researched candidates available for validation")
                return state
            
            # Get original candidate profiles
            candidate_profiles = {
                f"{c.full_name}_{c.cv_filename}": c 
                for c in state.get("parsed_candidates", [])
            }
            
            # Prepare validation pairs
            validation_pairs = []
            for research_result in state["researched_candidates"]:
                candidate = candidate_profiles.get(research_result.candidate_id)
                if candidate:
                    validation_pairs.append((candidate, research_result))
            
            if not validation_pairs:
                state["warnings"].append("No valid candidate-research pairs for validation")
                return state
            
            # Process validation in optimized batches
            validation_results = await self._validate_candidates_parallel(validation_pairs)
            
            state["validated_candidates"] = validation_results
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "validation",
                candidates_validated=len(validation_results),
                validation_passed=len([r for r in validation_results if r.is_valid]),
                validation_failed=len([r for r in validation_results if not r.is_valid]),
                average_confidence=sum(r.confidence_score for r in validation_results) / len(validation_results) if validation_results else 0,
                processing_time=total_time,
                validations_per_second=len(validation_results) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                batch_processing=True,
                parallel_validation=True
            )
            
            logger.info(f"🎯 Validation completed in {total_time:.2f}s for {len(validation_results)} candidates")
            logger.info(f"✅ Passed: {len([r for r in validation_results if r.is_valid])}, ❌ Failed: {len([r for r in validation_results if not r.is_valid])}")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized validation process failed: {e}")
            state["errors"].append(f"Validation process failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    async def _validate_candidates_parallel(self, validation_pairs: List[Tuple]) -> List[ValidationResult]:
        """Validate candidates in parallel batches"""
        
        validation_results = []
        batch_size = self.batch_processor.batch_size
        
        # Process in batches
        for i in range(0, len(validation_pairs), batch_size):
            batch = validation_pairs[i:i + batch_size]
            
            logger.info(f"🔄 Validating batch {i//batch_size + 1}/{(len(validation_pairs) + batch_size - 1)//batch_size}")
            
            # Create batch tasks
            batch_tasks = []
            for candidate, research_result in batch:
                task = asyncio.create_task(
                    self._validate_single_candidate_cached(candidate, research_result)
                )
                batch_tasks.append(task)
            
            # Execute batch with timeout
            try:
                batch_results = await asyncio.wait_for(
                    asyncio.gather(*batch_tasks, return_exceptions=True),
                    timeout=30  # 30 second timeout per batch
                )
                
                # Process results
                for result in batch_results:
                    if isinstance(result, ValidationResult):
                        validation_results.append(result)
                    elif isinstance(result, Exception):
                        logger.error(f"Batch validation error: {result}")
                
            except asyncio.TimeoutError:
                logger.warning("Validation batch timeout - continuing with partial results")
            
            # Brief pause between batches
            if i + batch_size < len(validation_pairs):
                await asyncio.sleep(0.5)
            
            # Memory cleanup between batches
            gc.collect()
        
        return validation_results
    
    async def _validate_single_candidate_cached(self, candidate: CandidateProfile, 
                                              research_result: ResearchResult) -> ValidationResult:
        """Validate single candidate with caching"""
        
        # Create cache key
        candidate_hash = hashlib.md5(candidate.get_candidate_id().encode()).hexdigest()[:8]
        research_hash = hashlib.md5(str(research_result.total_results_found).encode()).hexdigest()[:8]
        cache_key = f"validation_{candidate_hash}_{research_hash}"
        
        # Check cache
        cached_result = self.validation_cache.get(cache_key)
        if cached_result:
            self.cache_hits += 1
            return cached_result
        
        self.cache_misses += 1
        
        validation_result = ValidationResult(
            candidate_id=research_result.candidate_id,
            validation_criteria={}
        )
        
        try:
            start_time = time.time()
            
            # Perform optimized validation checks
            criteria_scores = await self._perform_validation_checks_optimized(candidate, research_result)
            
            # Set validation criteria results
            for criteria, score in criteria_scores.items():
                threshold = self.validation_criteria[criteria]["threshold"]
                validation_result.validation_criteria[criteria] = score >= threshold
            
            # Calculate weighted confidence score
            confidence_score = self._calculate_weighted_confidence(criteria_scores)
            validation_result.confidence_score = confidence_score
            
            # Determine if validation passed
            validation_result.is_valid = self._determine_validation_status(
                criteria_scores, confidence_score
            )
            
            # Generate validation notes
            validation_result.validation_notes = self._generate_validation_notes_optimized(
                candidate, criteria_scores
            )
            
            # Identify discrepancies
            validation_result.discrepancies = tuple(
                self._identify_discrepancies_fast(candidate, research_result, criteria_scores)
            )
            
            # Cache result
            self.validation_cache.put(cache_key, validation_result)
            
            # Track timing
            processing_time = time.time() - start_time
            self.validation_times[candidate.full_name] = processing_time
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Single candidate validation error for {candidate.full_name}: {e}")
            validation_result.validation_notes = f"Validation failed due to error: {str(e)}"
            return validation_result
    
    async def _perform_validation_checks_optimized(self, candidate: CandidateProfile, 
                                                  research_result: ResearchResult) -> Dict[str, float]:
        """Perform optimized validation checks"""
        
        criteria_scores = {}
        
        # 1. Name validation (optimized string matching)
        criteria_scores["name_match"] = self._validate_name_consistency_optimized(
            candidate, research_result
        )
        
        # 2. Email validation (regex-based)
        criteria_scores["email_consistency"] = self._validate_email_consistency_optimized(
            candidate, research_result
        )
        
        # 3. Location validation (pattern matching)
        criteria_scores["location_consistency"] = self._validate_location_consistency_optimized(
            candidate, research_result
        )
        
        # 4. Experience validation (vectorized)
        criteria_scores["experience_consistency"] = self._validate_experience_consistency_optimized(
            candidate, research_result
        )
        
        # 5. Skills validation (set operations)
        criteria_scores["skills_consistency"] = self._validate_skills_consistency_optimized(
            candidate, research_result
        )
        
        # 6. Education validation (fast matching)
        criteria_scores["education_consistency"] = self._validate_education_consistency_optimized(
            candidate, research_result
        )
        
        return criteria_scores
    
    def _validate_name_consistency_optimized(self, candidate: CandidateProfile, 
                                           research_result: ResearchResult) -> float:
        """Optimized name consistency validation"""
        
        candidate_name = self._normalize_name_fast(candidate.full_name)
        
        if not candidate_name:
            return 0.0
        
        found_names = []
        
        # Extract names from research results efficiently
        if research_result.linkedin_profile:
            linkedin_name = research_result.linkedin_profile.get("name", "")
            if linkedin_name:
                found_names.append(self._normalize_name_fast(linkedin_name))
        
        if research_result.github_profile:
            github_name = research_result.github_profile.get("name", "")
            if github_name:
                found_names.append(self._normalize_name_fast(github_name))
        
        # Quick check in additional profiles
        for profile in research_result.additional_profiles:
            title = profile.get("title", "")
            if title and candidate_name.split()[0] in title.lower():
                found_names.append(title.lower())
        
        if not found_names:
            return 0.5  # Neutral score
        
        # Calculate best similarity score efficiently
        best_similarity = 0.0
        candidate_parts = set(candidate_name.split())
        
        for found_name in found_names:
            if found_name:
                # Fast similarity using set operations
                found_parts = set(found_name.split())
                
                # Jaccard similarity (intersection over union)
                intersection = len(candidate_parts & found_parts)
                union = len(candidate_parts | found_parts)
                
                if union > 0:
                    similarity = intersection / union
                    best_similarity = max(best_similarity, similarity)
        
        return best_similarity
    
    def _validate_email_consistency_optimized(self, candidate: CandidateProfile, 
                                            research_result: ResearchResult) -> float:
        """Optimized email consistency validation"""
        
        candidate_email = candidate.email.lower() if candidate.email else ""
        
        if not candidate_email:
            return 0.5  # Neutral
        
        # Extract emails from research data efficiently
        all_text = ""
        
        if research_result.linkedin_profile:
            all_text += str(research_result.linkedin_profile)
        
        if research_result.github_profile:
            all_text += str(research_result.github_profile)
        
        # Use pre-compiled regex for performance
        found_emails = self.email_pattern.findall(all_text.lower())
        
        if not found_emails:
            return 0.6  # Slightly positive for privacy
        
        # Check for exact match
        if candidate_email in found_emails:
            return 1.0
        
        # Check domain consistency
        if '@' in candidate_email:
            candidate_domain = candidate_email.split('@')[1]
            found_domains = [email.split('@')[1] for email in found_emails if '@' in email]
            
            if candidate_domain in found_domains:
                return 0.8
        
        return 0.3
    
    def _validate_location_consistency_optimized(self, candidate: CandidateProfile, 
                                               research_result: ResearchResult) -> float:
        """Optimized location consistency validation"""
        
        candidate_location = candidate.location.lower() if candidate.location else ""
        
        if not candidate_location:
            return 0.5  # Neutral
        
        found_locations = []
        
        # Extract locations efficiently
        if research_result.linkedin_profile:
            linkedin_location = research_result.linkedin_profile.get("location", "")
            if linkedin_location:
                found_locations.append(linkedin_location.lower())
        
        if research_result.github_profile:
            github_location = research_result.github_profile.get("location", "")
            if github_location:
                found_locations.append(github_location.lower())
        
        if not found_locations:
            return 0.6  # Slightly positive
        
        # Fast location matching using set operations
        candidate_parts = set(candidate_location.replace(',', ' ').split())
        
        best_match = 0.0
        for location in found_locations:
            if location:
                found_parts = set(location.replace(',', ' ').split())
                
                # Calculate overlap
                intersection = len(candidate_parts & found_parts)
                if intersection > 0:
                    overlap_ratio = intersection / len(candidate_parts)
                    best_match = max(best_match, overlap_ratio)
        
        return best_match
    
    def _validate_experience_consistency_optimized(self, candidate: CandidateProfile, 
                                                 research_result: ResearchResult) -> float:
        """Optimized experience consistency validation"""
        
        if not candidate.work_experience:
            return 0.5
        
        # Extract company names efficiently
        cv_companies = {exp.get("company", "").lower().strip() 
                       for exp in candidate.work_experience 
                       if exp.get("company")}
        
        # Search for companies in research data
        found_companies = set()
        
        # Check all research text efficiently
        all_research_text = ""
        
        if research_result.linkedin_profile:
            all_research_text += str(research_result.linkedin_profile).lower()
        
        if research_result.github_profile:
            all_research_text += str(research_result.github_profile).lower()
        
        for profile in research_result.additional_profiles:
            all_research_text += f" {profile.get('title', '')} {profile.get('description', '')}".lower()
        
        # Fast company matching
        for company in cv_companies:
            if company and company in all_research_text:
                found_companies.add(company)
        
        if not cv_companies:
            return 0.5
        
        # Calculate consistency ratio
        consistency_ratio = len(found_companies) / len(cv_companies)
        return min(consistency_ratio * 1.5, 1.0)  # Boost for any matches
    
    def _validate_skills_consistency_optimized(self, candidate: CandidateProfile, 
                                             research_result: ResearchResult) -> float:
        """Optimized skills consistency validation"""
        
        # Combine all candidate skills efficiently
        cv_skills = set()
        cv_skills.update(skill.lower() for skill in candidate.technical_skills)
        cv_skills.update(skill.lower() for skill in candidate.programming_languages)
        cv_skills.update(skill.lower() for skill in candidate.frameworks)
        cv_skills.update(skill.lower() for skill in candidate.tools)
        
        if not cv_skills:
            return 0.5
        
        found_skills = set()
        
        # GitHub skills analysis (most reliable)
        if research_result.github_profile:
            github_languages = research_result.github_profile.get("languages", [])
            found_skills.update(lang.lower() for lang in github_languages)
            
            # Check repository descriptions
            repos = research_result.github_profile.get("repositories", [])
            for repo in repos:
                repo_lang = repo.get("language", "")
                if repo_lang:
                    found_skills.add(repo_lang.lower())
        
        # Quick skills matching using set operations
        skill_intersection = cv_skills & found_skills
        
        if not found_skills:
            return 0.4
        
        # Calculate skills consistency
        consistency_ratio = len(skill_intersection) / len(cv_skills)
        return min(consistency_ratio * 2.0, 1.0)  # Boost for technical validation
    
    def _validate_education_consistency_optimized(self, candidate: CandidateProfile, 
                                                research_result: ResearchResult) -> float:
        """Optimized education consistency validation"""
        
        if not candidate.education:
            return 0.5
        
        # Extract education keywords efficiently
        cv_education = set()
        for edu in candidate.education:
            university = edu.get("university", "").lower()
            degree = edu.get("degree", "").lower()
            if university:
                cv_education.update(university.split())
            if degree:
                cv_education.update(degree.split())
        
        # Search for education mentions in research data
        all_research_text = ""
        
        if research_result.linkedin_profile:
            all_research_text += str(research_result.linkedin_profile).lower()
        
        for profile in research_result.additional_profiles:
            all_research_text += f" {profile.get('title', '')} {profile.get('description', '')}".lower()
        
        # Fast education matching
        found_education = set(all_research_text.split()) & cv_education
        
        if not cv_education:
            return 0.5
        
        consistency_ratio = len(found_education) / len(cv_education)
        return min(consistency_ratio * 1.3, 1.0)
    
    def _calculate_weighted_confidence(self, criteria_scores: Dict[str, float]) -> float:
        """Calculate weighted confidence score"""
        
        total_weight = 0.0
        weighted_sum = 0.0
        
        for criteria, score in criteria_scores.items():
            weight = self.validation_criteria[criteria]["weight"]
            weighted_sum += score * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _determine_validation_status(self, criteria_scores: Dict[str, float], 
                                   confidence_score: float) -> bool:
        """Determine if validation passed using optimized logic"""
        
        # Critical criteria that must pass
        critical_criteria = ["name_match", "experience_consistency"]
        
        critical_passed = True
        for criteria in critical_criteria:
            if criteria in criteria_scores:
                threshold = self.validation_criteria[criteria]["threshold"]
                if criteria_scores[criteria] < threshold:
                    critical_passed = False
                    break
        
        # Overall criteria threshold
        passed_criteria = sum(
            1 for criteria, score in criteria_scores.items()
            if score >= self.validation_criteria[criteria]["threshold"]
        )
        
        criteria_ratio = passed_criteria / len(criteria_scores) if criteria_scores else 0
        
        # Validation passes if:
        # 1. Critical criteria pass AND
        # 2. At least 60% of all criteria pass AND
        # 3. Overall confidence is above 0.65
        return (critical_passed and 
                criteria_ratio >= 0.6 and 
                confidence_score >= 0.65)
    
    def _generate_validation_notes_optimized(self, candidate: CandidateProfile, 
                                           criteria_scores: Dict[str, float]) -> str:
        """Generate optimized validation notes"""
        
        notes = []
        
        # Overall confidence
        overall_confidence = self._calculate_weighted_confidence(criteria_scores)
        notes.append(f"Overall Confidence: {overall_confidence:.2f}")
        
        # Quick criteria summary
        passed_criteria = []
        failed_criteria = []
        
        for criteria, score in criteria_scores.items():
            threshold = self.validation_criteria[criteria]["threshold"]
            if score >= threshold:
                passed_criteria.append(criteria.replace('_', ' ').title())
            else:
                failed_criteria.append(criteria.replace('_', ' ').title())
        
        if passed_criteria:
            notes.append(f"✅ Passed: {', '.join(passed_criteria)}")
        
        if failed_criteria:
            notes.append(f"❌ Failed: {', '.join(failed_criteria)}")
        
        return " | ".join(notes)
    
    def _identify_discrepancies_fast(self, candidate: CandidateProfile, 
                                   research_result: ResearchResult, 
                                   criteria_scores: Dict[str, float]) -> List[str]:
        """Fast discrepancy identification"""
        
        discrepancies = []
        
        # Check significant failures only
        for criteria, score in criteria_scores.items():
            threshold = self.validation_criteria[criteria]["threshold"]
            
            if score < threshold * 0.7:  # Only significant failures
                if criteria == "name_match":
                    discrepancies.append("Name variations found in research")
                elif criteria == "email_consistency":
                    discrepancies.append("Email addresses inconsistent")
                elif criteria == "experience_consistency":
                    discrepancies.append("Work experience not well-documented online")
                elif criteria == "skills_consistency":
                    discrepancies.append("Technical skills not supported by online evidence")
        
        return discrepancies
    
    @staticmethod
    def _normalize_name_fast(name: str) -> str:
        """Fast name normalization"""
        
        if not name:
            return ""
        
        # Simple normalization
        normalized = name.lower().strip()
        
        # Remove common prefixes/suffixes
        prefixes = ['dr.', 'mr.', 'ms.', 'mrs.']
        suffixes = ['jr.', 'sr.', 'ii', 'iii']
        
        for prefix in prefixes:
            if normalized.startswith(prefix):
                normalized = normalized[len(prefix):].strip()
                break
        
        for suffix in suffixes:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
                break
        
        return ' '.join(normalized.split())  # Remove extra spaces
    
    async def handle_validation_failures_optimized(self, state: ThreadSafeState, 
                                                  failed_validations: List[ValidationResult]) -> ThreadSafeState:
        """Handle validation failures with optimization considerations"""
        
        if not failed_validations:
            return state
        
        logger.warning(f"⚠️ {len(failed_validations)} candidates failed validation")
        
        # Analyze failure patterns for optimization
        failure_patterns = {}
        for result in failed_validations:
            for criteria, passed in result.validation_criteria.items():
                if not passed:
                    failure_patterns[criteria] = failure_patterns.get(criteria, 0) + 1
        
        # Log failure patterns for system optimization
        if failure_patterns:
            most_common_failure = max(failure_patterns, key=failure_patterns.get)
            logger.info(f"Most common validation failure: {most_common_failure} ({failure_patterns[most_common_failure]} candidates)")
        
        # For candidates with very low confidence, consider re-research
        very_low_confidence = [r for r in failed_validations if r.confidence_score < 0.3]
        
        if very_low_confidence and len(very_low_confidence) < len(failed_validations) / 2:
            logger.warning(f"🔄 Consider re-research for {len(very_low_confidence)} candidates with very low confidence")
            state["warnings"].append(f"Consider re-research for {len(very_low_confidence)} candidates with very low confidence")
        
        # Add failure summary to state
        state = log_agent_message(
            state,
            "validation",
            f"Validation completed with {len(failed_validations)} failures. Common issue: {most_common_failure if failure_patterns else 'None'}",
            "warning"
        )
        
        return state
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "validation_times": self.validation_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "validation_cache_size": self.validation_cache.size(),
                "analysis_cache_size": self.analysis_cache.size()
            },
            "average_validation_time": sum(self.validation_times.values()) / len(self.validation_times) if self.validation_times else 0,
            "validation_criteria_weights": self.validation_criteria
        }
    
    def get_validation_summary(self, validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """Get comprehensive validation summary"""
        
        if not validation_results:
            return {"error": "No validation results to summarize"}
        
        summary = {
            "total_validated": len(validation_results),
            "passed_validation": len([r for r in validation_results if r.is_valid]),
            "failed_validation": len([r for r in validation_results if not r.is_valid]),
            "average_confidence": sum(r.confidence_score for r in validation_results) / len(validation_results),
            "criteria_analysis": {},
            "common_discrepancies": {},
            "confidence_distribution": {"high": 0, "medium": 0, "low": 0}
        }
        
        # Analyze criteria performance
        for criteria in self.validation_criteria.keys():
            passed = sum(1 for r in validation_results if r.validation_criteria.get(criteria, False))
            summary["criteria_analysis"][criteria] = {
                "passed": passed,
                "failed": len(validation_results) - passed,
                "pass_rate": passed / len(validation_results)
            }
        
        # Analyze common discrepancies
        all_discrepancies = []
        for result in validation_results:
            all_discrepancies.extend(result.discrepancies)
        
        for discrepancy in set(all_discrepancies):
            summary["common_discrepancies"][discrepancy] = all_discrepancies.count(discrepancy)
        
        # Confidence distribution
        for result in validation_results:
            if result.confidence_score >= 0.8:
                summary["confidence_distribution"]["high"] += 1
            elif result.confidence_score >= 0.6:
                summary["confidence_distribution"]["medium"] += 1
            else:
                summary["confidence_distribution"]["low"] += 1
        
        return summary
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.validation_cache.clear()
        self.analysis_cache.clear()
        
        if self.batch_processor:
            self.batch_processor.cleanup()
        
        gc.collect()

# Advanced validation utilities
class AdvancedValidationUtils:
    """Advanced utilities for validation optimization"""
    
    @staticmethod
    def calculate_identity_confidence(candidate: CandidateProfile, research_result: ResearchResult) -> float:
        """Calculate overall identity confidence using multiple factors"""
        
        confidence_factors = {}
        
        # Name similarity factor
        if research_result.linkedin_profile or research_result.github_profile:
            confidence_factors["name_match"] = 0.3
        
        # Digital footprint consistency
        platforms_found = len(research_result.platforms_searched)
        if platforms_found >= 2:
            confidence_factors["digital_presence"] = 0.2
        
        # Professional consistency
        if research_result.github_profile and candidate.technical_skills:
            confidence_factors["technical_alignment"] = 0.3
        
        # Information richness
        if research_result.total_results_found >= 3:
            confidence_factors["information_richness"] = 0.2
        
        return sum(confidence_factors.values())
    
    @staticmethod
    def detect_potential_identity_confusion(validation_results: List[ValidationResult]) -> List[Dict[str, Any]]:
        """Detect potential identity confusion cases"""
        
        confusion_cases = []
        
        for result in validation_results:
            risk_indicators = []
            
            # Very low name match but high other scores
            if (result.validation_criteria.get("name_match", True) == False and 
                result.confidence_score > 0.7):
                risk_indicators.append("High confidence with poor name match")
            
            # Inconsistent location but good other matches
            if (result.validation_criteria.get("location_consistency", True) == False and
                result.validation_criteria.get("experience_consistency", False) == True):
                risk_indicators.append("Location mismatch with experience match")
            
            # Email inconsistency with professional matches
            if (result.validation_criteria.get("email_consistency", True) == False and
                result.validation_criteria.get("skills_consistency", False) == True):
                risk_indicators.append("Email mismatch with skills match")
            
            if risk_indicators:
                confusion_cases.append({
                    "candidate_id": result.candidate_id,
                    "confidence_score": result.confidence_score,
                    "risk_indicators": risk_indicators,
                    "recommendation": "Manual review recommended"
                })
        
        return confusion_cases
    
    @staticmethod
    def generate_validation_insights(validation_results: List[ValidationResult]) -> Dict[str, Any]:
        """Generate insights for validation process improvement"""
        
        insights = {
            "process_effectiveness": {},
            "data_quality_indicators": {},
            "improvement_recommendations": []
        }
        
        if not validation_results:
            return insights
        
        # Process effectiveness analysis
        total_candidates = len(validation_results)
        high_confidence = len([r for r in validation_results if r.confidence_score >= 0.8])
        medium_confidence = len([r for r in validation_results if 0.6 <= r.confidence_score < 0.8])
        low_confidence = len([r for r in validation_results if r.confidence_score < 0.6])
        
        insights["process_effectiveness"] = {
            "high_confidence_rate": high_confidence / total_candidates,
            "medium_confidence_rate": medium_confidence / total_candidates,
            "low_confidence_rate": low_confidence / total_candidates,
            "overall_validation_rate": len([r for r in validation_results if r.is_valid]) / total_candidates
        }
        
        # Data quality indicators
        criteria_success_rates = {}
        validation_criteria = ["name_match", "email_consistency", "location_consistency", 
                             "experience_consistency", "skills_consistency", "education_consistency"]
        
        for criteria in validation_criteria:
            passed = sum(1 for r in validation_results if r.validation_criteria.get(criteria, False))
            criteria_success_rates[criteria] = passed / total_candidates
        
        insights["data_quality_indicators"] = criteria_success_rates
        
        # Generate improvement recommendations
        recommendations = []
        
        # Low name match rate
        if criteria_success_rates.get("name_match", 1.0) < 0.7:
            recommendations.append("Consider improving name normalization algorithms")
        
        # Low experience consistency
        if criteria_success_rates.get("experience_consistency", 1.0) < 0.6:
            recommendations.append("Enhance research scope for professional profiles")
        
        # Low skills consistency
        if criteria_success_rates.get("skills_consistency", 1.0) < 0.5:
            recommendations.append("Improve technical skills extraction from online profiles")
        
        # Overall low confidence
        if insights["process_effectiveness"]["high_confidence_rate"] < 0.5:
            recommendations.append("Consider additional research sources for better validation")
        
        insights["improvement_recommendations"] = recommendations
        
        return insights

# Validation reporting utilities
class ValidationReportGenerator:
    """Generate detailed validation reports"""
    
    @staticmethod
    def generate_validation_report(validation_results: List[ValidationResult], 
                                 candidate_profiles: Dict[str, CandidateProfile]) -> str:
        """Generate comprehensive validation report"""
        
        if not validation_results:
            return "No validation results available for reporting."
        
        report_lines = []
        
        # Header
        report_lines.extend([
            "CANDIDATE VALIDATION REPORT",
            "=" * 80,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Total Candidates Validated: {len(validation_results)}",
            ""
        ])
        
        # Summary statistics
        passed_count = len([r for r in validation_results if r.is_valid])
        failed_count = len(validation_results) - passed_count
        avg_confidence = sum(r.confidence_score for r in validation_results) / len(validation_results)
        
        report_lines.extend([
            "VALIDATION SUMMARY:",
            f"  ✅ Passed Validation: {passed_count} ({passed_count/len(validation_results)*100:.1f}%)",
            f"  ❌ Failed Validation: {failed_count} ({failed_count/len(validation_results)*100:.1f}%)",
            f"  📊 Average Confidence: {avg_confidence:.2f}",
            ""
        ])
        
        # Criteria analysis
        criteria_names = ["name_match", "email_consistency", "location_consistency", 
                         "experience_consistency", "skills_consistency", "education_consistency"]
        
        report_lines.extend([
            "CRITERIA PERFORMANCE:",
            "-" * 40
        ])
        
        for criteria in criteria_names:
            passed = sum(1 for r in validation_results if r.validation_criteria.get(criteria, False))
            rate = passed / len(validation_results) * 100
            status = "✅" if rate >= 70 else "⚠️" if rate >= 50 else "❌"
            
            report_lines.append(f"  {status} {criteria.replace('_', ' ').title()}: {passed}/{len(validation_results)} ({rate:.1f}%)")
        
        report_lines.append("")
        
        # Individual candidate results (top 10 and bottom 5)
        sorted_results = sorted(validation_results, key=lambda x: x.confidence_score, reverse=True)
        
        report_lines.extend([
            "TOP PERFORMERS (Highest Confidence):",
            "-" * 40
        ])
        
        for i, result in enumerate(sorted_results[:10], 1):
            candidate = candidate_profiles.get(result.candidate_id)
            candidate_name = candidate.full_name if candidate else "Unknown"
            status = "✅ VALID" if result.is_valid else "❌ REVIEW"
            
            report_lines.append(f"  {i:2d}. {candidate_name[:30]:<30} | {result.confidence_score:.2f} | {status}")
        
        report_lines.extend([
            "",
            "CANDIDATES NEEDING REVIEW (Lowest Confidence):",
            "-" * 40
        ])
        
        low_confidence_results = [r for r in sorted_results if r.confidence_score < 0.7]
        for i, result in enumerate(low_confidence_results[-5:], 1):
            candidate = candidate_profiles.get(result.candidate_id)
            candidate_name = candidate.full_name if candidate else "Unknown"
            
            report_lines.append(f"  {i}. {candidate_name[:30]:<30} | {result.confidence_score:.2f} | ⚠️ REVIEW")
            
            # Add discrepancies
            if result.discrepancies:
                for discrepancy in result.discrepancies[:2]:  # Show top 2
                    report_lines.append(f"     • {discrepancy}")
        
        # Common issues
        all_discrepancies = []
        for result in validation_results:
            all_discrepancies.extend(result.discrepancies)
        
        if all_discrepancies:
            discrepancy_counts = {}
            for disc in all_discrepancies:
                discrepancy_counts[disc] = discrepancy_counts.get(disc, 0) + 1
            
            top_issues = sorted(discrepancy_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            report_lines.extend([
                "",
                "COMMON VALIDATION ISSUES:",
                "-" * 40
            ])
            
            for issue, count in top_issues:
                percentage = count / len(validation_results) * 100
                report_lines.append(f"  • {issue}: {count} candidates ({percentage:.1f}%)")
        
        # Recommendations
        report_lines.extend([
            "",
            "RECOMMENDATIONS:",
            "-" * 40
        ])
        
        if failed_count > len(validation_results) * 0.3:
            report_lines.append("  • High failure rate - consider expanding research sources")
        
        if avg_confidence < 0.7:
            report_lines.append("  • Low average confidence - review validation criteria")
        
        high_name_failures = sum(1 for r in validation_results 
                               if not r.validation_criteria.get("name_match", True))
        if high_name_failures > len(validation_results) * 0.2:
            report_lines.append("  • High name mismatch rate - improve name normalization")
        
        report_lines.extend([
            "",
            "=" * 80,
            f"Report completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ])
        
        return "\n".join(report_lines)
    
    @staticmethod
    def generate_csv_validation_export(validation_results: List[ValidationResult],
                                     candidate_profiles: Dict[str, CandidateProfile]) -> str:
        """Generate CSV export of validation results"""
        
        lines = []
        
        # CSV Header
        headers = [
            "Candidate_Name", "Candidate_ID", "Is_Valid", "Confidence_Score",
            "Name_Match", "Email_Consistency", "Location_Consistency",
            "Experience_Consistency", "Skills_Consistency", "Education_Consistency",
            "Discrepancies", "Validation_Notes"
        ]
        
        lines.append(",".join(headers))
        
        # Data rows
        for result in validation_results:
            candidate = candidate_profiles.get(result.candidate_id)
            candidate_name = candidate.full_name if candidate else "Unknown"
            
            row = [
                f'"{candidate_name}"',
                f'"{result.candidate_id}"',
                str(result.is_valid),
                f"{result.confidence_score:.3f}",
                str(result.validation_criteria.get("name_match", False)),
                str(result.validation_criteria.get("email_consistency", False)),
                str(result.validation_criteria.get("location_consistency", False)),
                str(result.validation_criteria.get("experience_consistency", False)),
                str(result.validation_criteria.get("skills_consistency", False)),
                str(result.validation_criteria.get("education_consistency", False)),
                f'"{"; ".join(result.discrepancies)}"',
                f'"{result.validation_notes[:100]}..."' if len(result.validation_notes) > 100 else f'"{result.validation_notes}"'
            ]
            
            lines.append(",".join(row))
        
        return "\n".join(lines)

# Performance benchmarking
class ValidationBenchmark:
    """Benchmark validation performance"""
    
    def __init__(self):
        self.benchmarks = {}
        self.start_times = {}
    
    def start_benchmark(self, operation: str):
        """Start benchmarking an operation"""
        self.start_times[operation] = time.time()
    
    def end_benchmark(self, operation: str, candidates_processed: int = 1):
        """End benchmarking and record results"""
        if operation in self.start_times:
            duration = time.time() - self.start_times[operation]
            self.benchmarks[operation] = {
                "duration": duration,
                "candidates_processed": candidates_processed,
                "candidates_per_second": candidates_processed / duration if duration > 0 else 0,
                "timestamp": datetime.now().isoformat()
            }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        if not self.benchmarks:
            return {"message": "No benchmarks recorded"}
        
        summary = {
            "total_operations": len(self.benchmarks),
            "operations": self.benchmarks,
            "performance_metrics": {
                "fastest_operation": min(self.benchmarks.items(), key=lambda x: x[1]["duration"]),
                "slowest_operation": max(self.benchmarks.items(), key=lambda x: x[1]["duration"]),
                "highest_throughput": max(self.benchmarks.items(), key=lambda x: x[1]["candidates_per_second"]),
                "average_duration": sum(b["duration"] for b in self.benchmarks.values()) / len(self.benchmarks)
            }
        }
        
        return summary

# Export all optimized components
__all__ = [
    'OptimizedValidationAgent',
    'AdvancedValidationUtils', 
    'ValidationReportGenerator',
    'ValidationBenchmark'
]