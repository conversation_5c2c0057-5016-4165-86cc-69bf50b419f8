# matching_agent_enhanced_optimized.py
import asyncio
import logging
import json
import math
import re
import time
import gc
import hashlib
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from openai import OpenAI
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from state_management import (
    ThreadSafeState, CandidateProfile, MatchingResult, log_agent_message, 
    update_processing_stats, LRUCache, MemoryMonitor
)

logger = logging.getLogger(__name__)

class OptimizedEnhancedMatchingAgent:
    """
    High-performance matching agent with:
    - Async parallel processing
    - Intelligent caching
    - Vectorized calculations
    - Memory optimization
    - Batch AI analysis
    """
    
    def __init__(self, openai_api_key: str, cache=None, rate_limiter=None):
        self.client = OpenAI(api_key=openai_api_key)
        self.model = "gpt-4"
        self.cache = cache or LRUCache(maxsize=100)
        self.rate_limiter = rate_limiter
        
        # Performance optimizations
        self.max_workers = min(6, threading.active_count() + 2)
        self.vectorizer = TfidfVectorizer(
            stop_words='english', 
            max_features=1000,
            ngram_range=(1, 2),  # Include bigrams for better matching
            max_df=0.8,  # Ignore very common terms
            min_df=2     # Ignore very rare terms
        )
        
        # Caching for expensive operations
        self.text_cache = LRUCache(maxsize=200)
        self.vector_cache = LRUCache(maxsize=100)
        self.analysis_cache = LRUCache(maxsize=50)
        
        # Pre-compiled regex patterns
        self.experience_patterns = [
            re.compile(r'(\d+)\+?\s*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)', re.IGNORECASE),
            re.compile(r'(\d+)\+?\s*(?:years?|yrs?)\s*in', re.IGNORECASE),
            re.compile(r'minimum\s*(?:of\s*)?(\d+)\s*(?:years?|yrs?)', re.IGNORECASE),
            re.compile(r'at\s*least\s*(\d+)\s*(?:years?|yrs?)', re.IGNORECASE)
        ]
        
        # Technical skills vocabulary for faster matching
        self.tech_skills_vocab = {
            'programming': ['python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin'],
            'frameworks': ['react', 'angular', 'vue', 'django', 'flask', 'spring', 'laravel', 'express', 'nextjs'],
            'databases': ['mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'sqlite', 'oracle'],
            'cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform', 'jenkins'],
            'ai_ml': ['machine learning', 'deep learning', 'tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy']
        }
        
        # Performance tracking
        self.matching_times = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
    async def match_candidates_async(self, state: ThreadSafeState) -> ThreadSafeState:
        """Main async method to match candidates with optimizations"""
        
        try:
            logger.info("🎯 Starting high-performance candidate matching")
            state = log_agent_message(state, "matching", "Starting optimized candidate matching", "info")
            
            start_time = time.time()
            
            if not state.get("parsed_candidates"):
                state["errors"].append("No parsed candidates available for matching")
                return state
            
            candidates = state["parsed_candidates"]
            job_req = state["job_requirements"]
            
            # Phase 1: Pre-compute job requirements vectors and data
            job_context = await self._precompute_job_context(job_req)
            
            # Phase 2: Parallel candidate matching
            matching_results = await self._match_candidates_parallel(candidates, job_req, job_context)
            
            # Phase 3: Rank and filter results
            top_candidates = self._rank_and_filter_optimized(matching_results)
            
            state["matched_candidates"] = top_candidates
            
            # Performance metrics
            total_time = time.time() - start_time
            
            # Update statistics
            state = update_processing_stats(
                state,
                "matching",
                total_candidates_evaluated=len(candidates),
                candidates_matched=len(top_candidates),
                average_match_score=np.mean([r.match_score for r in top_candidates]) if top_candidates else 0,
                processing_time=total_time,
                candidates_per_second=len(candidates) / total_time if total_time > 0 else 0,
                cache_hit_ratio=self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                parallel_processing=True,
                vectorized_calculations=True
            )
            
            logger.info(f"🏆 Matching completed in {total_time:.2f}s. Selected {len(top_candidates)} top candidates")
            logger.info(f"💾 Cache hit ratio: {self.cache_hits/(self.cache_hits + self.cache_misses)*100:.1f}%")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized matching process failed: {e}")
            state["errors"].append(f"Matching process failed: {str(e)}")
            return state
        finally:
            # Memory cleanup
            gc.collect()
    
    async def _precompute_job_context(self, job_req) -> Dict[str, Any]:
        """Pre-compute job context for efficient matching"""
        
        # Create cache key for job requirements
        job_hash = hashlib.md5(
            f"{job_req.position_title}{job_req.job_description}".encode()
        ).hexdigest()[:12]
        
        cache_key = f"job_context_{job_hash}"
        cached_context = self.cache.get(cache_key)
        
        if cached_context:
            self.cache_hits += 1
            logger.debug("💾 Job context cache hit")
            return cached_context
        
        self.cache_misses += 1
        
        # Compute job context
        context = {
            'all_keywords': set(job_req.get_all_keywords()),
            'skill_set': job_req.get_skill_set(),
            'required_skills_lower': [skill.lower() for skill in job_req.required_skills],
            'preferred_skills_lower': [skill.lower() for skill in job_req.preferred_skills],
            'job_text': f"{job_req.position_title} {job_req.job_description}".lower(),
            'experience_requirement': self._extract_experience_requirement_optimized(job_req.job_description),
            'job_vector': None,  # Will be computed with candidates
            'position_level': self._determine_position_level(job_req.position_title),
            'domain_keywords': self._extract_domain_keywords(job_req.job_description)
        }
        
        # Cache the result
        self.cache.put(cache_key, context)
        
        return context
    
    async def _match_candidates_parallel(self, candidates: List[CandidateProfile], 
                                       job_req, job_context: Dict[str, Any]) -> List[MatchingResult]:
        """Match candidates in parallel with batch processing"""
        
        logger.info(f"🔄 Processing {len(candidates)} candidates in parallel")
        
        # Create batches for processing
        batch_size = min(10, len(candidates))
        matching_results = []
        
        # Process in batches to manage memory and API limits
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i + batch_size]
            
            logger.info(f"📊 Processing batch {i//batch_size + 1}/{(len(candidates) + batch_size - 1)//batch_size}")
            
            # Create async tasks for batch
            batch_tasks = []
            
            for candidate in batch:
                task = asyncio.create_task(
                    self._calculate_comprehensive_match_cached(candidate, job_req, job_context)
                )
                batch_tasks.append(task)
            
            # Wait for batch completion
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, MatchingResult):
                    matching_results.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"Batch matching error: {result}")
            
            # Rate limiting between batches
            if self.rate_limiter:
                await self.rate_limiter.wait("matching_batch")
            
            # Memory cleanup between batches
            if i % (batch_size * 2) == 0:
                gc.collect()
        
        return matching_results
    
    async def _calculate_comprehensive_match_cached(self, candidate: CandidateProfile, 
                                                  job_req, job_context: Dict[str, Any]) -> Optional[MatchingResult]:
        """Calculate comprehensive match score with caching"""
        
        try:
            # Create cache key
            candidate_hash = hashlib.md5(
                f"{candidate.full_name}_{candidate.cv_filename}".encode()
            ).hexdigest()[:8]
            job_hash = hashlib.md5(job_req.job_description.encode()).hexdigest()[:8]
            cache_key = f"match_{candidate_hash}_{job_hash}"
            
            # Check cache
            cached_result = self.analysis_cache.get(cache_key)
            if cached_result:
                self.cache_hits += 1
                return cached_result
            
            self.cache_misses += 1
            start_time = time.time()
            
            # 1. Fast keyword matching (35%)
            keyword_score = self._calculate_keyword_match_optimized(candidate, job_context)
            
            # 2. Vectorized skills similarity (30%)
            skills_score = self._calculate_skills_match_vectorized(candidate, job_context)
            
            # 3. Experience level matching (20%)
            experience_score = self._calculate_experience_match_optimized(candidate, job_context)
            
            # 4. Education matching (10%)
            education_score = self._calculate_education_match_fast(candidate, job_req)
            
            # 5. AI-powered semantic analysis (5%) - only for top candidates
            semantic_score = 50.0  # Default neutral score
            
            # Calculate weighted final score
            final_score = (
                keyword_score * 0.35 +
                skills_score * 0.30 +
                experience_score * 0.20 +
                education_score * 0.10 +
                semantic_score * 0.05
            )
            
            # Generate detailed analysis only if score is promising
            if final_score >= 60:
                analysis_result = await self._generate_detailed_analysis_optimized(
                    candidate, job_req, {
                        "keyword_score": keyword_score,
                        "skills_score": skills_score,
                        "experience_score": experience_score,
                        "education_score": education_score,
                        "semantic_score": semantic_score,
                        "final_score": final_score
                    }
                )
            else:
                # Quick analysis for low-scoring candidates
                analysis_result = self._generate_quick_analysis(candidate, final_score)
            
            # Create result
            result = MatchingResult(
                candidate_id=candidate.get_candidate_id(),
                match_score=final_score,
                ranking=0,  # Will be set during ranking
                match_reasons=tuple(analysis_result.get("match_reasons", [])),
                strengths=tuple(analysis_result.get("strengths", [])),
                concerns=tuple(analysis_result.get("concerns", [])),
                detailed_analysis=analysis_result.get("detailed_analysis", "")
            )
            
            # Cache result
            self.analysis_cache.put(cache_key, result)
            
            # Track timing
            processing_time = time.time() - start_time
            self.matching_times[candidate.full_name] = processing_time
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating match for {candidate.full_name}: {e}")
            return None
    
    def _calculate_keyword_match_optimized(self, candidate: CandidateProfile, job_context: Dict[str, Any]) -> float:
        """Optimized keyword matching using pre-computed sets"""
        
        # Get cached candidate text
        candidate_text = self._get_candidate_text_cached(candidate)
        candidate_words = set(candidate_text.lower().split())
        
        # Use pre-computed job keywords
        job_keywords = job_context['all_keywords']
        
        if not job_keywords:
            return 50.0  # Neutral score
        
        # Calculate intersection
        matched_keywords = len(job_keywords & candidate_words)
        total_keywords = len(job_keywords)
        
        # Base score
        match_ratio = matched_keywords / total_keywords
        score = match_ratio * 100
        
        # Bonus for exact skill matches
        required_skills = job_context['required_skills_lower']
        skill_matches = sum(1 for skill in required_skills if skill in candidate_text.lower())
        
        if required_skills:
            skill_bonus = (skill_matches / len(required_skills)) * 20
            score += skill_bonus
        
        return min(score, 100.0)
    
    def _calculate_skills_match_vectorized(self, candidate: CandidateProfile, job_context: Dict[str, Any]) -> float:
        """Vectorized skills matching for performance"""
        
        # Combine all candidate skills
        candidate_skills = list(candidate.technical_skills) + list(candidate.programming_languages) + \
                          list(candidate.frameworks) + list(candidate.tools)
        
        if not candidate_skills:
            return 30.0  # Low score for no skills
        
        candidate_skills_text = " ".join(candidate_skills).lower()
        
        # Required skills matching (weighted higher)
        required_skills = job_context['required_skills_lower']
        preferred_skills = job_context['preferred_skills_lower']
        
        required_matches = 0
        preferred_matches = 0
        
        # Vectorized matching using sets for performance
        candidate_skill_set = set(candidate_skills_text.split())
        
        for skill in required_skills:
            if any(skill_word in candidate_skill_set for skill_word in skill.split()):
                required_matches += 1
        
        for skill in preferred_skills:
            if any(skill_word in candidate_skill_set for skill_word in skill.split()):
                preferred_matches += 1
        
        # Calculate scores
        required_score = (required_matches / len(required_skills)) * 100 if required_skills else 50
        preferred_score = (preferred_matches / len(preferred_skills)) * 100 if preferred_skills else 50
        
        # Weighted combination (80% required, 20% preferred)
        final_score = (required_score * 0.8) + (preferred_score * 0.2)
        
        # Bonus for skill diversity
        unique_skills = len(set(skill.lower() for skill in candidate_skills))
        diversity_bonus = min(unique_skills * 2, 15)  # Max 15 points bonus
        
        return min(final_score + diversity_bonus, 100.0)
    
    def _calculate_experience_match_optimized(self, candidate: CandidateProfile, job_context: Dict[str, Any]) -> float:
        """Optimized experience matching"""
        
        candidate_years = candidate.years_experience
        required_years = job_context['experience_requirement']
        position_level = job_context['position_level']
        
        if required_years == 0:
            # Use position level as fallback
            level_requirements = {
                'entry': 0, 'junior': 1, 'mid': 3, 'senior': 6, 'lead': 8, 'principal': 10, 'director': 12
            }
            required_years = level_requirements.get(position_level, 3)
        
        # Calculate experience score
        if candidate_years >= required_years:
            # Perfect score for meeting requirement
            base_score = 100.0
            
            # Small bonus for more experience, but diminishing returns
            excess_years = candidate_years - required_years
            bonus = min(excess_years * 2, 10)  # Max 10 points bonus
            
            return min(base_score + bonus, 100.0)
        else:
            # Penalty for less experience
            gap = required_years - candidate_years
            penalty = gap * 12  # 12 points per year gap
            
            return max(100 - penalty, 0)
    
    def _calculate_education_match_fast(self, candidate: CandidateProfile, job_req) -> float:
        """Fast education matching"""
        
        if not candidate.education:
            return 40.0  # Lower score for no education data
        
        education_text = " ".join([
            f"{edu.get('degree', '')} {edu.get('major', '')} {edu.get('university', '')}"
            for edu in candidate.education
        ]).lower()
        
        job_education = f"{job_req.job_description} {job_req.education_requirements}".lower()
        
        # Quick degree level matching
        degree_levels = {
            'phd': 5, 'doctoral': 5, 'doctorate': 5,
            'master': 4, 'mba': 4,
            'bachelor': 3, 'bachelors': 3,
            'associate': 2
        }
        
        candidate_level = 0
        required_level = 0
        
        for degree, level in degree_levels.items():
            if degree in education_text:
                candidate_level = max(candidate_level, level)
            if degree in job_education:
                required_level = max(required_level, level)
        
        if required_level == 0:
            return 70.0  # Neutral if no clear requirement
        
        if candidate_level >= required_level:
            return 100.0
        elif candidate_level > 0:
            return 50 + (candidate_level / required_level) * 40
        else:
            return 30.0
    
    async def _generate_detailed_analysis_optimized(self, candidate: CandidateProfile, 
                                                   job_req, scores: Dict[str, float]) -> Dict[str, Any]:
        """Generate detailed analysis only for promising candidates"""
        
        try:
            # Rate limiting for AI calls
            if self.rate_limiter:
                await self.rate_limiter.wait("ai_analysis")
            
            candidate_summary = self._create_candidate_summary_cached(candidate)
            
            prompt = f"""Analyze this candidate's fit for the job. Be specific and concise.
            
CANDIDATE: {candidate_summary}

JOB: {job_req.position_title} at {job_req.company_name}
Required: {', '.join(job_req.required_skills[:5])}

SCORES: Keyword: {scores['keyword_score']:.1f}, Skills: {scores['skills_score']:.1f}, 
Experience: {scores['experience_score']:.1f}, Final: {scores['final_score']:.1f}

Return JSON:
{{
    "match_reasons": ["Reason 1", "Reason 2", "Reason 3"],
    "strengths": ["Strength 1", "Strength 2", "Strength 3"],
    "concerns": ["Concern 1", "Concern 2"],
    "detailed_analysis": "2-sentence analysis"
}}"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2,
                max_tokens=500
            )
            
            content = response.choices[0].message.content.strip()
            
            # Parse JSON response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                return json.loads(content)
                
        except Exception as e:
            logger.error(f"Detailed analysis error: {e}")
            return self._generate_quick_analysis(candidate, scores['final_score'])
    
    def _generate_quick_analysis(self, candidate: CandidateProfile, final_score: float) -> Dict[str, Any]:
        """Generate quick analysis for lower-scoring candidates"""
        
        strengths = []
        concerns = []
        match_reasons = []
        
        # Quick heuristic analysis
        if candidate.years_experience > 5:
            strengths.append("Experienced professional")
        if len(candidate.technical_skills) > 10:
            strengths.append("Diverse technical skill set")
        if candidate.education:
            strengths.append("Formal education background")
        
        if final_score < 50:
            concerns.append("Limited alignment with job requirements")
        if candidate.years_experience < 2:
            concerns.append("Limited professional experience")
        
        match_reasons.append(f"Overall match score: {final_score:.1f}/100")
        
        return {
            "match_reasons": match_reasons,
            "strengths": strengths[:3],
            "concerns": concerns[:2],
            "detailed_analysis": f"Candidate shows {final_score:.1f}% alignment with job requirements."
        }
    
    def _rank_and_filter_optimized(self, matching_results: List[MatchingResult]) -> List[MatchingResult]:
        """Optimized ranking and filtering"""
        
        if not matching_results:
            return []
        
        # Sort by match score (descending) - vectorized operation
        sorted_results = sorted(matching_results, key=lambda x: x.match_score, reverse=True)
        
        # Assign rankings efficiently
        for i, result in enumerate(sorted_results):
            result.ranking = i + 1
        
        # Smart filtering based on score distribution
        scores = [r.match_score for r in sorted_results]
        
        # Use statistical approach for filtering
        mean_score = np.mean(scores)
        std_score = np.std(scores) if len(scores) > 1 else 0
        
        # Dynamic threshold based on score distribution
        min_threshold = max(50, mean_score - std_score)
        
        # Keep top candidates above threshold
        filtered_results = []
        for result in sorted_results:
            if len(filtered_results) < 7:  # Always keep top 7
                filtered_results.append(result)
            elif result.match_score >= min_threshold and len(filtered_results) < 12:
                filtered_results.append(result)
            else:
                break
        
        return filtered_results
    
    def _get_candidate_text_cached(self, candidate: CandidateProfile) -> str:
        """Get candidate text with caching"""
        
        cache_key = f"text_{candidate.get_candidate_id()}"
        cached_text = self.text_cache.get(cache_key)
        
        if cached_text:
            return cached_text
        
        # Build candidate text
        text_parts = [
            candidate.summary,
            candidate.current_position,
            candidate.current_company,
            " ".join(candidate.technical_skills),
            " ".join(candidate.programming_languages),
            " ".join(candidate.frameworks),
            " ".join(candidate.tools)
        ]
        
        # Add work experience
        for exp in candidate.work_experience:
            text_parts.extend([
                exp.get("position", ""),
                exp.get("company", ""),
                " ".join(exp.get("responsibilities", [])),
                " ".join(exp.get("technologies", []))
            ])
        
        candidate_text = " ".join([text for text in text_parts if text])
        
        # Cache result
        self.text_cache.put(cache_key, candidate_text)
        
        return candidate_text
    
    def _create_candidate_summary_cached(self, candidate: CandidateProfile) -> str:
        """Create cached candidate summary"""
        
        cache_key = f"summary_{candidate.get_candidate_id()}"
        cached_summary = self.text_cache.get(cache_key)
        
        if cached_summary:
            return cached_summary
        
        summary_parts = [
            f"Name: {candidate.full_name}",
            f"Experience: {candidate.years_experience} years",
            f"Current: {candidate.current_position} at {candidate.current_company}",
            f"Skills: {', '.join(list(candidate.technical_skills)[:8])}",
            f"Languages: {', '.join(list(candidate.programming_languages)[:5])}"
        ]
        
        if candidate.education:
            edu = candidate.education[0]
            summary_parts.append(f"Education: {edu.get('degree', '')} from {edu.get('university', '')}")
        
        summary = "\n".join([part for part in summary_parts if part.split(': ')[1]])
        
        # Cache result
        self.text_cache.put(cache_key, summary)
        
        return summary
    
    def _extract_experience_requirement_optimized(self, job_description: str) -> int:
        """Optimized experience requirement extraction"""
        
        job_lower = job_description.lower()
        
        # Use pre-compiled patterns for performance
        for pattern in self.experience_patterns:
            match = pattern.search(job_lower)
            if match:
                return int(match.group(1))
        
        return 0
    
    def _determine_position_level(self, position_title: str) -> str:
        """Determine position level from title"""
        
        title_lower = position_title.lower()
        
        level_keywords = {
            'entry': ['entry', 'junior', 'associate', 'trainee', 'intern'],
            'mid': ['mid', 'intermediate', 'regular'],
            'senior': ['senior', 'sr.', 'lead'],
            'principal': ['principal', 'staff', 'architect'],
            'director': ['director', 'vp', 'vice president', 'head of', 'chief']
        }
        
        for level, keywords in level_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                return level
        
        return 'mid'  # Default
    
    def _extract_domain_keywords(self, job_description: str) -> List[str]:
        """Extract domain-specific keywords"""
        
        domain_patterns = {
            'fintech': ['finance', 'banking', 'payment', 'trading', 'fintech'],
            'healthcare': ['health', 'medical', 'clinical', 'healthcare'],
            'ecommerce': ['ecommerce', 'retail', 'shopping', 'marketplace'],
            'enterprise': ['enterprise', 'b2b', 'saas', 'erp', 'crm']
        }
        
        desc_lower = job_description.lower()
        found_domains = []
        
        for domain, keywords in domain_patterns.items():
            if any(keyword in desc_lower for keyword in keywords):
                found_domains.append(domain)
        
        return found_domains
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        
        return {
            "matching_times": self.matching_times,
            "cache_statistics": {
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "text_cache_size": self.text_cache.size(),
                "vector_cache_size": self.vector_cache.size(),
                "analysis_cache_size": self.analysis_cache.size()
            },
            "average_matching_time": sum(self.matching_times.values()) / len(self.matching_times) if self.matching_times else 0
        }
    
    def cleanup(self):
        """Cleanup caches and resources"""
        
        self.text_cache.clear()
        self.vector_cache.clear()
        self.analysis_cache.clear()
        
        gc.collect()

# Export optimized components
__all__ = ['OptimizedEnhancedMatchingAgent']