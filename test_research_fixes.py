#!/usr/bin/env python3
"""
Comprehensive test script to verify GitHub API fixes and research agent improvements
"""

import asyncio
import aiohttp
import logging
import os
import sys
import time
from typing import Dict, Any, List

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import project modules
from state_management import CandidateProfile, ThreadSafeState, JobRequirements
from agents.research_agent_enhanced import OptimizedEnhancedResearchAgent

class ResearchAgentTester:
    """Test class for the enhanced research agent"""
    
    def __init__(self, github_token: str = None, serp_api_key: str = None, tavily_api_key: str = None):
        self.github_token = github_token
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def create_test_candidate(self, name: str, github_url: str = None) -> CandidateProfile:
        """Create a test candidate profile"""
        return CandidateProfile(
            full_name=name,
            email=f"{name.lower().replace(' ', '.')}@example.com",
            phone="555-0123",
            current_position="Software Engineer",
            current_company="Tech Corp",
            github_url=github_url,
            linkedin_url=f"https://linkedin.com/in/{name.lower().replace(' ', '-')}",
            skills=["Python", "Machine Learning", "Data Science"],
            experience_years=5,
            education="Bachelor's in Computer Science",
            cv_filename=f"{name.replace(' ', '_')}_resume.pdf"
        )
    
    def create_test_state(self, candidates: List[CandidateProfile]) -> ThreadSafeState:
        """Create a test state with candidates"""
        
        job_requirements = JobRequirements(
            position_title="Senior Data Scientist",
            job_description="Looking for an experienced data scientist with Python and ML skills",
            required_skills=["Python", "Machine Learning", "Data Science"],
            preferred_skills=["TensorFlow", "PyTorch"],
            experience_level="Senior",
            education_requirements="Bachelor's degree in Computer Science or related field",
            location="Remote",
            keywords=["data science", "machine learning", "python"]
        )
        
        # Create mock match results
        from state_management import MatchResult
        matched_candidates = [
            MatchResult(
                candidate_id=candidate.get_candidate_id(),
                match_score=0.85,
                skills_match=0.9,
                experience_match=0.8,
                education_match=0.85,
                matching_details={"skills": ["Python", "Machine Learning"]}
            )
            for candidate in candidates
        ]
        
        state = ThreadSafeState()
        state["job_requirements"] = job_requirements
        state["parsed_candidates"] = candidates
        state["matched_candidates"] = matched_candidates
        state["errors"] = []
        state["warnings"] = []
        
        return state
    
    async def test_github_api_functionality(self) -> Dict[str, Any]:
        """Test GitHub API functionality directly"""
        
        logger.info("=== Testing GitHub API Functionality ===")
        
        try:
            # Create research agent
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",  # Not used for GitHub API test
                github_token=self.github_token,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                session=self.session
            )
            
            # Test with a known GitHub user
            test_url = "https://github.com/octocat"
            
            logger.info(f"Testing GitHub API extraction for: {test_url}")
            result = await agent._extract_github_profile_api(test_url)
            
            if result:
                logger.info(f"✅ GitHub API test successful!")
                logger.info(f"   Username: {result.get('username')}")
                logger.info(f"   Name: {result.get('name')}")
                logger.info(f"   Public repos: {result.get('public_repos')}")
                logger.info(f"   Followers: {result.get('followers')}")
                logger.info(f"   Data collection method: {result.get('data_collection_method')}")
                
                return {
                    'status': 'success',
                    'username': result.get('username'),
                    'public_repos': result.get('public_repos'),
                    'followers': result.get('followers'),
                    'has_repositories': len(result.get('repositories', [])) > 0,
                    'has_languages': len(result.get('languages', [])) > 0,
                    'authenticated': self.github_token is not None
                }
            else:
                logger.warning("❌ GitHub API test failed - no data returned")
                return {'status': 'failed', 'error': 'No data returned'}
                
        except Exception as e:
            logger.error(f"❌ GitHub API test error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def test_research_agent_timeout_handling(self) -> Dict[str, Any]:
        """Test research agent timeout handling"""
        
        logger.info("=== Testing Research Agent Timeout Handling ===")
        
        try:
            # Create test candidates
            candidates = [
                self.create_test_candidate("John Doe", "https://github.com/octocat"),
                self.create_test_candidate("Jane Smith", "https://github.com/torvalds")
            ]
            
            # Create test state
            state = self.create_test_state(candidates)
            
            # Create research agent with shorter timeouts for testing
            agent = OptimizedEnhancedResearchAgent(
                openai_api_key="test-key",
                github_token=self.github_token,
                serp_api_key=self.serp_api_key,
                tavily_api_key=self.tavily_api_key,
                session=self.session
            )
            
            # Override timeouts for testing
            agent.request_timeout = 10  # Shorter timeout
            agent.github_api_timeout = 8
            
            logger.info("Starting research with timeout testing...")
            start_time = time.time()
            
            # Run research
            result_state = await agent.research_candidates_optimized(state)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Check results
            researched_candidates = result_state.get("researched_candidates", [])
            errors = result_state.get("errors", [])
            warnings = result_state.get("warnings", [])
            
            logger.info(f"✅ Research completed in {processing_time:.2f} seconds")
            logger.info(f"   Researched candidates: {len(researched_candidates)}")
            logger.info(f"   Errors: {len(errors)}")
            logger.info(f"   Warnings: {len(warnings)}")
            
            # Check for GitHub data
            github_success = 0
            for result in researched_candidates:
                if result.github_profile:
                    github_success += 1
                    logger.info(f"   GitHub data found for: {result.candidate_id}")
            
            return {
                'status': 'success',
                'processing_time': processing_time,
                'candidates_researched': len(researched_candidates),
                'github_profiles_found': github_success,
                'errors': len(errors),
                'warnings': len(warnings),
                'timeout_handled': processing_time < 120  # Should complete within 2 minutes
            }
            
        except Exception as e:
            logger.error(f"❌ Research agent test error: {e}")
            return {'status': 'error', 'error': str(e)}

async def main():
    """Main test function"""
    
    # Get API keys from environment
    github_token = os.getenv('GITHUB_TOKEN')
    serp_api_key = os.getenv('SERP_API_KEY')
    tavily_api_key = os.getenv('TAVILY_API_KEY')
    
    logger.info("🧪 Starting Research Agent Fix Tests")
    logger.info(f"GitHub Token: {'✅ Available' if github_token else '❌ Not found'}")
    logger.info(f"SERP API Key: {'✅ Available' if serp_api_key else '❌ Not found'}")
    logger.info(f"Tavily API Key: {'✅ Available' if tavily_api_key else '❌ Not found'}")
    
    async with ResearchAgentTester(github_token, serp_api_key, tavily_api_key) as tester:
        
        # Test 1: GitHub API functionality
        github_result = await tester.test_github_api_functionality()
        print(f"\n📊 GitHub API Test Result: {github_result}")
        
        # Test 2: Research agent timeout handling
        timeout_result = await tester.test_research_agent_timeout_handling()
        print(f"\n📊 Timeout Handling Test Result: {timeout_result}")
        
        # Summary
        print("\n" + "="*50)
        print("🎯 TEST SUMMARY")
        print("="*50)
        
        if github_result.get('status') == 'success':
            print("✅ GitHub API: Working correctly")
        else:
            print(f"❌ GitHub API: {github_result.get('error', 'Failed')}")
        
        if timeout_result.get('status') == 'success':
            print("✅ Timeout Handling: Working correctly")
            print(f"   Processing time: {timeout_result.get('processing_time', 0):.2f}s")
            print(f"   GitHub profiles found: {timeout_result.get('github_profiles_found', 0)}")
        else:
            print(f"❌ Timeout Handling: {timeout_result.get('error', 'Failed')}")
        
        print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
