# orchestrator_agent_optimized.py
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import gc
from contextlib import asynccontextmanager

from state_management import (
    ThreadSafeState, JobRequirements, log_agent_message, update_processing_stats,
    MemoryMonitor, profiler, BatchProcessor, AsyncStateManager
)

logger = logging.getLogger(__name__)

class OptimizedOrchestratorAgent:
    """
    High-performance orchestrator with parallel processing, async optimization,
    and intelligent resource management
    """
    
    def __init__(self, openai_api_key: str, serp_api_key: str = None, 
                 tavily_api_key: str = None, github_token: str = None,
                 session=None, rate_limiter=None, response_cache=None):
        self.api_key = openai_api_key
        self.serp_api_key = serp_api_key
        self.tavily_api_key = tavily_api_key
        self.github_token = github_token
        self.session = session
        self.rate_limiter = rate_limiter
        self.response_cache = response_cache
        
        # Performance optimization settings
        self.max_parallel_agents = min(8, (threading.active_count() or 1) + 4)
        self.batch_processor = BatchProcessor(batch_size=5, max_workers=4)
        self.memory_threshold = 200 * 1024 * 1024  # 200MB
        
        # Workflow configuration
        self.workflow_steps = [
            "resume_analysis",
            "matching", 
            "research",
            "validation",
            "summarization"
        ]
        
        # Performance tracking
        self.step_timings = {}
        self.memory_snapshots = {}
        
    async def execute_workflow(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute optimized workflow with parallel processing and memory management"""
        
        try:
            logger.info("🚀 Starting high-performance orchestrated workflow")
            state = log_agent_message(state, "orchestrator", "Starting optimized workflow", "info")
            
            # Start performance monitoring
            profiler.start_timing("total_workflow")
            
            # Initialize workflow with memory monitoring
            state = await self._initialize_workflow_optimized(state)
            
            # Execute steps with optimizations
            for step_index, step in enumerate(self.workflow_steps):
                step_start = time.time()
                memory_before = MemoryMonitor.get_memory_usage()
                
                logger.info(f"📋 Executing optimized step {step_index + 1}/{len(self.workflow_steps)}: {step}")
                
                # Profile each step
                profiler.start_timing(f"step_{step}")
                
                # Execute step with error handling and optimization
                state = await self._execute_step_optimized(state, step, step_index)
                
                # End profiling
                profiler.end_timing(f"step_{step}")
                
                # Monitor memory after each step
                memory_after = MemoryMonitor.get_memory_usage()
                step_time = time.time() - step_start
                
                # Log performance metrics
                logger.info(f"✅ Step {step} completed in {step_time:.2f}s")
                logger.info(f"💾 Memory: {memory_before['rss_mb']}MB -> {memory_after['rss_mb']}MB")
                
                # Update state with performance metrics
                state = update_processing_stats(
                    state, 
                    f"{step}_performance",
                    step_duration=step_time,
                    memory_before=memory_before['rss_mb'],
                    memory_after=memory_after['rss_mb'],
                    memory_delta=memory_after['rss_mb'] - memory_before['rss_mb']
                )
                
                # Check for errors after each step
                if state.get("errors"):
                    logger.error(f"❌ Errors in step {step}: {state['errors']}")
                    state = log_agent_message(state, "orchestrator", f"Errors in {step}: {state['errors']}", "error")
                
                # Memory cleanup between steps
                if memory_after['rss_mb'] > self.memory_threshold / (1024 * 1024):
                    logger.info("🧹 Performing memory cleanup")
                    await self._perform_memory_cleanup(state)
                
                # Update current step
                state["current_step"] = step
                
                # Brief pause to allow other operations
                await asyncio.sleep(0.1)
            
            # Finalize workflow
            state = await self._finalize_workflow_optimized(state)
            
            # End total timing
            profiler.end_timing("total_workflow")
            
            # Add performance summary to state
            state["performance_summary"] = profiler.get_summary()
            
            logger.info("✅ Optimized workflow completed successfully")
            state = log_agent_message(state, "orchestrator", "High-performance workflow completed", "info")
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized workflow execution failed: {e}")
            state["errors"].append(f"Workflow execution failed: {str(e)}")
            state = log_agent_message(state, "orchestrator", f"Workflow failed: {str(e)}", "error")
            return state
        finally:
            # Cleanup resources
            await self._cleanup_resources()
    
    async def _initialize_workflow_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Initialize workflow with performance optimizations"""
        
        logger.info("🔧 Initializing optimized workflow")
        
        # Validate inputs efficiently
        if not state["uploaded_files"]:
            state["errors"].append("No files uploaded")
            return state
        
        if not state["job_requirements"].job_description:
            state["errors"].append("No job description provided")
            return state
        
        # Pre-process job requirements for better performance
        job_req = state["job_requirements"]
        
        # Cache processed keywords for reuse
        all_keywords = job_req.get_all_keywords()
        skill_set = job_req.get_skill_set()
        
        # Initialize processing stats with optimization metrics
        state = update_processing_stats(
            state, 
            "initialization",
            total_files=len(state["uploaded_files"]),
            workflow_start_time=datetime.now().isoformat(),
            max_parallel_agents=self.max_parallel_agents,
            memory_optimization_enabled=True,
            caching_enabled=bool(self.response_cache),
            rate_limiting_enabled=bool(self.rate_limiter),
            preprocessed_keywords=len(all_keywords),
            skill_set_size=len(skill_set)
        )
        
        # Start async memory monitoring
        asyncio.create_task(self._monitor_memory_continuously())
        
        logger.info(f"📊 Optimized workflow initialized for {len(state['uploaded_files'])} files")
        logger.info(f"🚄 Using {self.max_parallel_agents} parallel agents")
        logger.info(f"💾 Memory monitoring active, threshold: {self.memory_threshold // (1024*1024)}MB")
        
        return state
    
    async def _execute_step_optimized(self, state: ThreadSafeState, step: str, step_index: int) -> ThreadSafeState:
        """Execute workflow step with optimizations"""
        
        try:
            logger.info(f"⚙️ Executing optimized {step}")
            
            if step == "resume_analysis":
                state = await self._execute_resume_analysis_parallel(state)
                
            elif step == "matching":
                state = await self._execute_matching_optimized(state)
                
            elif step == "research":
                state = await self._execute_research_cached(state)
                
            elif step == "validation":
                state = await self._execute_validation_batch(state)
                
            elif step == "summarization":
                state = await self._execute_summarization_parallel(state)
            
            state["current_step"] = step
            
            logger.info(f"✅ Optimized step {step} completed")
            return state
            
        except Exception as e:
            logger.error(f"❌ Optimized step {step} failed: {e}")
            state["errors"].append(f"Step {step} failed: {str(e)}")
            return state
    
    async def _execute_resume_analysis_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute resume analysis with parallel processing"""
        
        from agents.resume_analysis_agent import OptimizedResumeAnalysisAgent
        
        # Create agent with optimizations
        agent = OptimizedResumeAnalysisAgent(
            self.api_key,
            cache=self.response_cache,
            batch_processor=self.batch_processor
        )
        
        # Execute with parallel processing
        state = await agent.analyze_resumes_parallel(state)
        
        return state
    
    async def _execute_matching_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute matching with async optimization"""
        
        from agents.matching_agent_enhanced import OptimizedEnhancedMatchingAgent
        
        agent = OptimizedEnhancedMatchingAgent(
            self.api_key,
            cache=self.response_cache,
            rate_limiter=self.rate_limiter
        )
        
        state = await agent.match_candidates_async(state)
        
        return state
    
    async def _execute_research_cached(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute research with caching and rate limiting"""
        
        from agents.research_agent_enhanced import OptimizedEnhancedResearchAgent
        
        agent = OptimizedEnhancedResearchAgent(
            openai_api_key=self.api_key,
            serp_api_key=self.serp_api_key,
            tavily_api_key=self.tavily_api_key,
            github_token=self.github_token,
            session=self.session,
            rate_limiter=self.rate_limiter,
            cache=self.response_cache
        )
        
        state = await agent.research_candidates_optimized(state)
        
        return state
    
    async def _execute_validation_batch(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute validation with batch processing"""
        
        from agents.validation_agent import OptimizedValidationAgent
        
        agent = OptimizedValidationAgent(
            self.api_key,
            batch_processor=self.batch_processor,
            cache=self.response_cache
        )
        
        state = await agent.validate_candidates_batch(state)
        
        return state
    
    async def _execute_summarization_parallel(self, state: ThreadSafeState) -> ThreadSafeState:
        """Execute summarization with parallel report generation"""
        
        from agents.summarization_agent import OptimizedSummarizationAgent
        
        agent = OptimizedSummarizationAgent(
            self.api_key,
            batch_processor=self.batch_processor,
            cache=self.response_cache
        )
        
        state = await agent.create_final_reports_parallel(state)
        
        return state
    
    async def _finalize_workflow_optimized(self, state: ThreadSafeState) -> ThreadSafeState:
        """Finalize workflow with performance summary"""
        
        logger.info("🏁 Finalizing optimized workflow")
        
        # Calculate final processing metrics
        total_time = self._calculate_processing_time(state)
        final_memory = MemoryMonitor.get_memory_usage()
        
        # Update final stats
        state = update_processing_stats(
            state,
            "finalization",
            workflow_end_time=datetime.now().isoformat(),
            total_processing_time=total_time,
            final_candidates_count=len(state.get("validated_candidates", [])),
            reports_generated=len(state.get("candidate_reports", {})),
            final_memory_usage=final_memory['rss_mb'],
            memory_efficiency_score=self._calculate_memory_efficiency(state),
            parallel_processing_effectiveness=self._calculate_parallel_effectiveness(state)
        )
        
        # Generate optimized workflow summary
        state = self._generate_optimized_workflow_summary(state)
        
        # Final memory cleanup
        await self._perform_final_cleanup(state)
        
        logger.info("🎉 Optimized workflow finalized")
        return state
    
    def _calculate_processing_time(self, state: ThreadSafeState) -> float:
        """Calculate total processing time"""
        
        try:
            start_time = datetime.fromisoformat(state["processing_stats"]["workflow_start_time"])
            end_time = datetime.now()
            return (end_time - start_time).total_seconds()
        except:
            return 0.0
    
    def _calculate_memory_efficiency(self, state: ThreadSafeState) -> float:
        """Calculate memory efficiency score (0-100)"""
        
        try:
            stats = state["processing_stats"]
            
            # Base score
            score = 100.0
            
            # Penalize for high memory usage
            final_memory = stats.get("final_memory_usage", 0)
            if final_memory > 500:  # >500MB
                score -= 30
            elif final_memory > 300:  # >300MB
                score -= 15
            
            # Bonus for memory cleanup effectiveness
            memory_deltas = [
                stats.get(f"{step}_performance", {}).get("memory_delta", 0)
                for step in self.workflow_steps
            ]
            
            avg_memory_delta = sum(memory_deltas) / len(memory_deltas) if memory_deltas else 0
            if avg_memory_delta < 10:  # Low memory growth per step
                score += 10
            
            return max(0, min(100, score))
        except:
            return 50.0
    
    def _calculate_parallel_effectiveness(self, state: ThreadSafeState) -> float:
        """Calculate parallel processing effectiveness (0-100)"""
        
        try:
            stats = state["processing_stats"]
            
            # Calculate based on processing speed
            total_files = stats.get("total_files", 1)
            total_time = stats.get("total_processing_time", 1)
            
            # Files per second
            throughput = total_files / total_time
            
            # Score based on throughput (assuming 1 file/sec as baseline)
            if throughput > 2.0:
                return 100.0
            elif throughput > 1.5:
                return 85.0
            elif throughput > 1.0:
                return 70.0
            elif throughput > 0.5:
                return 50.0
            else:
                return 30.0
        except:
            return 50.0
    
    def _generate_optimized_workflow_summary(self, state: ThreadSafeState) -> ThreadSafeState:
        """Generate comprehensive workflow execution summary"""
        
        stats = state["processing_stats"]
        
        summary = f"""
🚀 HIGH-PERFORMANCE WORKFLOW EXECUTION SUMMARY
{'='*60}

📊 Processing Statistics:
- Total files processed: {stats.get('total_files', 0)}
- Successfully parsed resumes: {len(state.get('parsed_candidates', []))}
- Candidates matched: {len(state.get('matched_candidates', []))}
- Candidates researched: {len(state.get('researched_candidates', []))}
- Candidates validated: {len(state.get('validated_candidates', []))}
- Final reports generated: {len(state.get('candidate_reports', {}))}

⚡ Performance Metrics:
- Total processing time: {stats.get('total_processing_time', 0):.2f} seconds
- Memory efficiency score: {stats.get('memory_efficiency_score', 0):.1f}/100
- Parallel effectiveness: {stats.get('parallel_processing_effectiveness', 0):.1f}/100
- Final memory usage: {stats.get('final_memory_usage', 0):.1f}MB

🚄 Optimization Features Used:
- Parallel processing: ✅ ({self.max_parallel_agents} agents)
- Async optimization: ✅
- Intelligent caching: {'✅' if self.response_cache else '❌'}
- Adaptive rate limiting: {'✅' if self.rate_limiter else '❌'}
- Memory management: ✅
- Batch operations: ✅

⏱️ Step Timing Breakdown:
"""
        
        # Add step timings
        for step in self.workflow_steps:
            step_stats = stats.get(f"{step}_performance", {})
            duration = step_stats.get("step_duration", 0)
            memory_delta = step_stats.get("memory_delta", 0)
            summary += f"- {step.replace('_', ' ').title()}: {duration:.2f}s (Δ{memory_delta:+.1f}MB)\n"
        
        summary += f"""
⚠️ Issues:
- Errors: {len(state.get('errors', []))}
- Warnings: {len(state.get('warnings', []))}

🎯 Job Requirements:
- Position: {state['job_requirements'].position_title}
- Required skills: {', '.join(state['job_requirements'].required_skills[:5])}
- Experience level: {state['job_requirements'].experience_level}
"""
        
        state["workflow_summary"] = summary
        logger.info(summary)
        
        return state
    
    async def _perform_memory_cleanup(self, state: ThreadSafeState):
        """Perform intelligent memory cleanup"""
        
        logger.info("🧹 Performing memory cleanup")
        
        # Clean up large candidate data
        for candidate in state.get("parsed_candidates", []):
            if hasattr(candidate, 'cleanup_large_data'):
                candidate.cleanup_large_data()
        
        # Limit research data
        research_candidates = state.get("researched_candidates", [])
        for research in research_candidates:
            if hasattr(research, 'additional_profiles') and len(research.additional_profiles) > 10:
                research.additional_profiles = research.additional_profiles[:10]
        
        # Clear old cache entries if cache is getting large
        if self.response_cache and hasattr(self.response_cache, 'clear'):
            cache_size = getattr(self.response_cache, '__len__', lambda: 0)()
            if cache_size > 500:
                # Clear oldest 25% of cache
                if hasattr(self.response_cache, 'clear'):
                    # Simple clear for now - could be more sophisticated
                    pass
        
        # Force garbage collection
        gc.collect()
        
        # Log memory after cleanup
        memory_after = MemoryMonitor.get_memory_usage()
        logger.info(f"💾 Memory after cleanup: {memory_after['rss_mb']}MB")
    
    async def _perform_final_cleanup(self, state: ThreadSafeState):
        """Perform final cleanup and resource management"""
        
        logger.info("🧹 Performing final cleanup")
        
        # Clean up batch processor
        if self.batch_processor:
            self.batch_processor.cleanup()
        
        # Final memory cleanup
        await self._perform_memory_cleanup(state)
        
        # Clear temporary data
        if "raw_resumes" in state:
            # Keep only essential data, clear raw content
            for resume in state["raw_resumes"]:
                if len(resume.get("content", "")) > 1000:
                    # Keep only first 1000 chars for reference
                    resume["content"] = resume["content"][:1000] + "... [truncated for memory]"
    
    async def _monitor_memory_continuously(self):
        """Continuously monitor memory usage"""
        
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                memory_usage = MemoryMonitor.get_memory_usage()
                
                if memory_usage["percent"] > 85:
                    logger.warning(f"🚨 High memory usage: {memory_usage['percent']}%")
                    
                    # Force cleanup
                    MemoryMonitor.force_cleanup()
                    
                elif memory_usage["percent"] > 70:
                    logger.info(f"⚠️ Elevated memory usage: {memory_usage['percent']}%")
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                break
    
    async def _cleanup_resources(self):
        """Cleanup all resources"""
        
        try:
            # Cleanup batch processor
            if hasattr(self, 'batch_processor') and self.batch_processor:
                self.batch_processor.cleanup()
            
            # Final garbage collection
            gc.collect()
            
        except Exception as e:
            logger.error(f"Resource cleanup error: {e}")
    
    def get_workflow_status(self, state: ThreadSafeState) -> Dict[str, Any]:
        """Get current workflow status with performance metrics"""
        
        current_memory = MemoryMonitor.get_memory_usage()
        
        return {
            "current_step": state.get("current_step", "unknown"),
            "total_steps": len(self.workflow_steps),
            "completed_steps": self.workflow_steps.index(state.get("current_step", "")) + 1 if state.get("current_step") in self.workflow_steps else 0,
            "processing_stats": state.get("processing_stats", {}),
            "errors": state.get("errors", []),
            "warnings": state.get("warnings", []),
            "candidates_processed": {
                "parsed": len(state.get("parsed_candidates", [])),
                "matched": len(state.get("matched_candidates", [])),
                "researched": len(state.get("researched_candidates", [])),
                "validated": len(state.get("validated_candidates", []))
            },
            "performance_metrics": {
                "memory_usage_mb": current_memory['rss_mb'],
                "memory_percent": current_memory['percent'],
                "parallel_agents": self.max_parallel_agents,
                "caching_enabled": bool(self.response_cache),
                "rate_limiting_enabled": bool(self.rate_limiter)
            }
        }
    
    async def handle_error_recovery_optimized(self, state: ThreadSafeState, failed_step: str) -> ThreadSafeState:
        """Handle error recovery with optimization considerations"""
        
        logger.warning(f"🔄 Attempting optimized error recovery for step: {failed_step}")
        
        # Log error recovery attempt
        state = log_agent_message(
            state, 
            "orchestrator", 
            f"Attempting optimized recovery for failed step: {failed_step}", 
            "warning"
        )
        
        # Implement optimization-aware recovery strategies
        if failed_step == "resume_analysis":
            # Reduce batch size and retry
            self.batch_processor.batch_size = max(1, self.batch_processor.batch_size // 2)
            state["warnings"].append("Retrying resume analysis with reduced batch size")
            
        elif failed_step == "research":
            # Reduce parallel requests and increase delays
            if self.rate_limiter:
                # Increase base delay
                self.rate_limiter.base_delay *= 2
            state["warnings"].append("Continuing with reduced research parallelism")
            
        elif failed_step == "validation":
            # Skip complex validation and use simplified approach
            state["warnings"].append("Using simplified validation due to errors")
        
        # Force memory cleanup after error
        await self._perform_memory_cleanup(state)
        
        return state

# Utility functions for creating workflow config
def create_optimized_workflow_config(job_requirements: JobRequirements) -> Dict[str, Any]:
    """Create optimized workflow configuration"""
    
    # Determine optimal settings based on job requirements
    skills_count = len(job_requirements.required_skills + job_requirements.preferred_skills)
    
    config = {
        "resume_analysis": {
            "extraction_depth": "comprehensive",
            "skills_focus": job_requirements.required_skills,
            "parse_timeout": 30,
            "parallel_processing": True,
            "batch_size": min(10, max(3, skills_count // 2))
        },
        "matching": {
            "min_score_threshold": 0.6,
            "max_candidates": 10,
            "ranking_criteria": ["skills_match", "experience_level", "education"],
            "async_processing": True,
            "cache_results": True
        },
        "research": {
            "platforms": ["linkedin", "github", "google_scholar", "personal_websites"],
            "max_results_per_platform": 5,
            "research_timeout": 60,
            "parallel_requests": 3,
            "cache_duration": 3600,
            "adaptive_rate_limiting": True
        },
        "validation": {
            "confidence_threshold": 0.8,
            "validation_criteria": ["name_match", "email_match", "experience_consistency"],
            "max_retry_attempts": 2,
            "batch_processing": True,
            "memory_optimization": True
        },
        "summarization": {
            "report_format": "comprehensive",
            "include_research_data": True,
            "generate_rankings": True,
            "parallel_report_generation": True,
            "streaming_output": True
        },
        "performance": {
            "max_parallel_agents": 8,
            "memory_threshold_mb": 200,
            "cache_enabled": True,
            "monitoring_enabled": True,
            "cleanup_interval": 60
        }
    }
    
    return config

# Main optimized workflow runner
async def run_orchestrated_workflow_optimized(
    job_requirements: JobRequirements,
    uploaded_files: List[str],
    openai_api_key: str,
    serp_api_key: str = None,
    tavily_api_key: str = None,
    github_token: str = None,
    session=None,
    rate_limiter=None,
    response_cache=None
) -> ThreadSafeState:
    """Main entry point for optimized orchestrated workflow"""
    
    # Create initial state
    from state_management import create_initial_state
    state = create_initial_state(job_requirements, uploaded_files)
    
    # Create optimized orchestrator with all resources
    orchestrator = OptimizedOrchestratorAgent(
        openai_api_key=openai_api_key,
        serp_api_key=serp_api_key,
        tavily_api_key=tavily_api_key,
        github_token=github_token,
        session=session,
        rate_limiter=rate_limiter,
        response_cache=response_cache
    )
    
    # Execute optimized workflow
    final_state = await orchestrator.execute_workflow(state)
    
    return final_state

# Performance monitoring context manager
@asynccontextmanager
async def performance_monitoring():
    """Context manager for performance monitoring"""
    
    start_time = time.time()
    start_memory = MemoryMonitor.get_memory_usage()
    
    try:
        yield
    finally:
        end_time = time.time()
        end_memory = MemoryMonitor.get_memory_usage()
        
        duration = end_time - start_time
        memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']
        
        logger.info(f"⚡ Operation completed in {duration:.2f}s, memory delta: {memory_delta:+.1f}MB")

# Export optimized components
__all__ = [
    'OptimizedOrchestratorAgent',
    'run_orchestrated_workflow_optimized',
    'create_optimized_workflow_config',
    'performance_monitoring'
]