2025-06-02 16:28:07,207 - __main__ - INFO - === Testing GitHub Search Query Generation ===
2025-06-02 16:28:07,403 - __main__ - INFO - === Testing Web Search Functionality ===
2025-06-02 16:28:08,565 - agents.research_agent_enhanced - ERROR - Tavily search error: Unauthorized: missing or invalid API key.
2025-06-02 16:28:08,572 - __main__ - INFO - === Testing GitHub URL Validation ===
2025-06-02 16:28:08,681 - __main__ - INFO -    Valid: True
2025-06-02 16:28:08,681 - __main__ - INFO -    Username: torvalds
2025-06-02 16:28:08,682 - __main__ - INFO -    Valid: True
2025-06-02 16:28:08,682 - __main__ - INFO -    Username: octocat
2025-06-02 16:28:08,684 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,684 - __main__ - INFO -    Username: torvalds
2025-06-02 16:28:08,685 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,685 - __main__ - INFO -    Username: None
2025-06-02 16:28:08,685 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,686 - __main__ - INFO -    Username: None
2025-06-02 16:28:08,686 - __main__ - INFO -    Valid: True
2025-06-02 16:28:08,686 - __main__ - INFO -    Username: john-doe
2025-06-02 16:28:08,687 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,687 - __main__ - INFO -    Username: jane
2025-06-02 16:28:08,688 - __main__ - INFO -    Valid: True
2025-06-02 16:28:08,688 - __main__ - INFO -    Username: user123
2025-06-02 16:28:08,689 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,689 - __main__ - INFO -    Username: None
2025-06-02 16:28:08,690 - __main__ - INFO -    Valid: False
2025-06-02 16:28:08,691 - __main__ - INFO -    Username: None
2025-06-02 16:28:08,691 - __main__ - INFO - === Testing Full GitHub Discovery Pipeline ===
